#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Deutsch Button Functionality
Simple test to verify if the German language button works
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_deutsch_button():
    """Test if the Deutsch button functionality works"""
    print("🇩🇪 Testing Deutsch Button Functionality")
    print("=" * 50)
    
    try:
        # Test the language manager directly
        from language_manager import set_global_language, get_global_language
        from translatable_screen import tr
        
        print("1. Testing direct language manager...")
        
        # Test setting to German
        success = set_global_language("Deutsch")
        if success:
            current = get_global_language()
            print(f"   ✅ Language set to: {current}")
            
            # Test some German translations
            test_translations = {
                "HOME": tr("HOME"),
                "SETTINGS": tr("SETTINGS"),
                "BATTERY": tr("BATTERY"),
                "WiFi": tr("WiFi"),
                "ON": tr("ON"),
                "OFF": tr("OFF")
            }
            
            print("   🔤 German translations:")
            for key, translation in test_translations.items():
                print(f"     {key} → {translation}")
            
            # Check if translations are actually German
            german_count = 0
            for key, translation in test_translations.items():
                if translation != key:  # Different from English = translated
                    german_count += 1
            
            coverage = (german_count / len(test_translations)) * 100
            print(f"   📊 German coverage: {coverage:.1f}%")
            
        else:
            print("   ❌ Failed to set language to Deutsch")
            return False
        
        print("\n2. Testing SettingsScreen method...")
        
        # Test the SettingsScreen method directly
        import main
        settings = main.SettingsScreen()
        
        print("   🔧 Calling set_language_german()...")
        result = settings.set_language_german()
        
        if result is not False:
            print("   ✅ set_language_german() executed successfully")
            
            # Check if language actually changed
            current_lang = get_global_language()
            print(f"   🌍 Current language after method call: {current_lang}")
            
            if current_lang == "Deutsch":
                print("   ✅ Language successfully changed to Deutsch!")
                return True
            else:
                print(f"   ⚠️ Language is {current_lang}, expected Deutsch")
                return False
        else:
            print("   ❌ set_language_german() returned False")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_language_buttons():
    """Test all language buttons"""
    print("\n🌍 Testing All Language Buttons")
    print("=" * 50)
    
    try:
        import main
        settings = main.SettingsScreen()
        
        # Test all language methods
        language_tests = [
            ("English", settings.set_language_english),
            ("Polski", settings.set_language_polish),
            ("Deutsch", settings.set_language_german),
            ("Русский", settings.set_language_russian)
        ]
        
        working_buttons = 0
        
        for lang_name, method in language_tests:
            print(f"\n   🔄 Testing {lang_name} button...")
            
            try:
                result = method()
                
                if result is not False:
                    working_buttons += 1
                    print(f"     ✅ {lang_name} button works")
                    
                    # Verify language actually changed
                    from language_manager import get_global_language
                    current = get_global_language()
                    if current == lang_name:
                        print(f"     ✅ Language confirmed: {current}")
                    else:
                        print(f"     ⚠️ Expected {lang_name}, got {current}")
                else:
                    print(f"     ❌ {lang_name} button returned False")
                    
            except Exception as e:
                print(f"     ❌ {lang_name} button error: {e}")
        
        print(f"\n📊 Working buttons: {working_buttons}/{len(language_tests)}")
        return working_buttons >= 3  # At least 3 out of 4 should work
        
    except Exception as e:
        print(f"❌ All buttons test failed: {e}")
        return False

def test_ui_translation_updates():
    """Test if UI translations update properly"""
    print("\n🖥️ Testing UI Translation Updates")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Test switching between languages and checking key translations
        languages = ["English", "Deutsch", "Polski", "Русский"]
        key_translations = ["SETTINGS", "DISPLAY", "LANGUAGE", "WiFi", "ON", "OFF"]
        
        for language in languages:
            print(f"\n   🔄 Testing {language}:")
            set_global_language(language)
            
            for key in key_translations[:3]:  # Test first 3 keys
                translation = tr(key)
                print(f"     {key} → {translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI translation test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Deutsch Button & Translation System Test")
    print("=" * 70)
    
    tests = [
        ("Deutsch Button", test_deutsch_button),
        ("All Language Buttons", test_all_language_buttons),
        ("UI Translation Updates", test_ui_translation_updates)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*70}")
        print(f"🧪 Running: {test_name}")
        print(f"{'='*70}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    # Final results
    print(f"\n{'='*70}")
    print(f"📊 FINAL RESULTS")
    print(f"{'='*70}")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED!")
        print("🇩🇪 Deutsch button is working perfectly!")
        print("🌍 All language buttons are functional!")
        return True
    elif passed >= 2:
        print("✅ MOSTLY WORKING!")
        print("🇩🇪 Deutsch button should be working!")
        print("🔧 Minor issues may exist.")
        return True
    else:
        print("⚠️ Some issues found.")
        print("🔧 Deutsch button may need attention.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
