#:kivy 2.0.0

<FuelScreen>:
    FloatLayout:
        canvas.before:
            Color:
                rgba: 0.05, 0.1, 0.15, 1  # Dark blue background
            Rectangle:
                pos: self.pos
                size: self.size

        # Header with back button and icons
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 0, 0]
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            Widget:
                size_hint_x: 0.8  # empty space in the middle
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # FUEL title
        Label:
            id: fuel_title
            text: app.get_translation('FUEL') if app else 'FUEL'
            font_size: '24sp'
            size_hint: None, None
            size: self.texture_size
            pos_hint: {'center_x': 0.5, 'top': 0.92}

        # Clock
        ClockWidget:
            id: clock
            font_size: '30sp'
            pos_hint: {'center_x': 0.5, 'top': 0.86}
            size_hint: None, None

        # Fuel Level Display
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.8, 0.25
            pos_hint: {'center_x': 0.5, 'top': 0.8}
            spacing: 10

            Label:
                id: fuel_level_title
                text: app.get_translation('FUEL LEVEL') if app else 'FUEL LEVEL'
                font_size: '20sp'
                size_hint_y: None
                height: 30

            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 80
                spacing: 20

                # Percentage display
                Label:
                    id: fuel_level_label
                    text: f"{int(app.fuel_level)}%"
                    font_size: '48sp'
                    size_hint_x: 0.3

                # Progress bar
                BoxLayout:
                    orientation: 'vertical'
                    size_hint_x: 0.7
                    padding: [0, 20, 0, 20]

                    ProgressBar:
                        id: fuel_level_bar
                        value: app.fuel_level
                        max: 100
                        size_hint_y: None
                        height: 40
                        background_color: 0.1, 0.1, 0.1, 1
                        color: 0.2, 0.7, 0.3, 1 if app.fuel_level > 20 else 0.8, 0.2, 0.2, 1

        # Fuel System Information
        GridLayout:
            cols: 2
            size_hint: 0.9, 0.35
            pos_hint: {'center_x': 0.5, 'center_y': 0.4}
            spacing: 15
            padding: 10

            # Fuel Tank
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: fuel_tank_title
                    text: app.get_translation('FUEL TANK') if app else 'FUEL TANK'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'icons/fuel.png'
                    size_hint_y: None
                    height: 48
                    pos_hint: {'center_x': 0.5}

                Label:
                    text: f"{int(app.fuel_level)}%"
                    font_size: '28sp'
                    color: 0.9, 0.95, 1, 1

                Label:
                    text: f"{int(app.fuel_level * 3)} liters remaining"
                    font_size: '14sp'
                    color: 0.7, 0.75, 0.8, 1

            # Fuel Consumption
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: fuel_consumption_title
                    text: app.get_translation('FUEL CONSUMPTION') if app else 'FUEL CONSUMPTION'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'icons/consumption.png'
                    size_hint_y: None
                    height: 48
                    pos_hint: {'center_x': 0.5}

                Label:
                    text: '5.2 L/h' if app.engine_status == "active" else '0 L/h'
                    font_size: '28sp'
                    color: 0.9, 0.95, 1, 1

                Label:
                    id: fuel_current_consumption_label
                    text: app.get_translation('Current consumption') if app else 'Current consumption'
                    font_size: '14sp'
                    color: 0.7, 0.75, 0.8, 1

        # Range Estimation
        BoxLayout:
            size_hint: 0.5, 0.15
            pos_hint: {'center_x': 0.5, 'center_y': 0.15}
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]

            BoxLayout:
                orientation: 'vertical'
                padding: 10
                
                Label:
                    id: range_estimation_title
                    text: app.get_translation('RANGE ESTIMATION') if app else 'RANGE ESTIMATION'
                    font_size: '16sp'
                
                GridLayout:
                    cols: 2
                    spacing: 5
                    
                    Label:
                        id: estimated_range_label
                        text: app.get_translation('Estimated Range:') if app else 'Estimated Range:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: f"{int(app.fuel_level * 3 / 5.2)} hours" if app.engine_status == "active" else "N/A"
                        font_size: '14sp'
                        halign: 'left'
                    
                    Label:
                        id: estimated_distance_label
                        text: app.get_translation('Estimated Distance:') if app else 'Estimated Distance:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: f"{int(app.fuel_level * 3 / 5.2 * 8)} nm" if app.engine_status == "active" else "N/A"
                        font_size: '14sp'
                        halign: 'left'
