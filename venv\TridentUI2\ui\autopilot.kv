#:kivy 2.0.0

<AutopilotScreen>:
    FloatLayout:
        canvas.before:
            Color:
                rgba: 0.05, 0.1, 0.15, 1  # Dark blue background
            Rectangle:
                pos: self.pos
                size: self.size

        # Header with back button and icons
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 0, 0]
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            Widget:
                size_hint_x: 0.8  # empty space in the middle
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # AUTOPILOT title
        Label:
            id: autopilot_title
            text: app.get_translation('AUTOPILOT') if app else 'AUTOPILOT'
            font_size: '24sp'
            size_hint: None, None
            size: self.texture_size
            pos_hint: {'center_x': 0.5, 'top': 0.92}

        # Clock
        ClockWidget:
            id: clock
            font_size: '30sp'
            pos_hint: {'center_x': 0.5, 'top': 0.86}
            size_hint: None, None

        # Autopilot Status Display
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.8, 0.2
            pos_hint: {'center_x': 0.5, 'top': 0.8}
            spacing: 10

            Label:
                id: autopilot_status_title
                text: app.get_translation('AUTOPILOT STATUS') if app else 'AUTOPILOT STATUS'
                font_size: '20sp'
                size_hint_y: None
                height: 30

            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 60
                spacing: 20

                # Status indicator
                Label:
                    id: autopilot_status_label
                    text: (app.get_translation("ACTIVE") if app.autopilot else app.get_translation("INACTIVE")) if app else ("ACTIVE" if app.autopilot else "INACTIVE")
                    font_size: '48sp'
                    color: (0, 0.8, 0, 1) if app.autopilot else (0.8, 0.3, 0.3, 1)
                    size_hint_x: 0.5

                # Autopilot image
                Image:
                    source: 'icons/autopilot.png'
                    size_hint_x: 0.5
                    allow_stretch: True
                    keep_ratio: True

        # Autopilot Controls
        GridLayout:
            cols: 2
            size_hint: 0.9, 0.35
            pos_hint: {'center_x': 0.5, 'center_y': 0.4}
            spacing: 15
            padding: 10

            # Autopilot Activation
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: autopilot_control_title
                    text: app.get_translation('AUTOPILOT CONTROL') if app else 'AUTOPILOT CONTROL'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'icons/autopilot.png'
                    size_hint_y: None
                    height: 48
                    pos_hint: {'center_x': 0.5}

                Button:
                    text: (app.get_translation('DEACTIVATE') if app.autopilot else app.get_translation('ACTIVATE')) if app else ('DEACTIVATE' if app.autopilot else 'ACTIVATE')
                    font_size: '22sp'
                    background_color: (0.8, 0.3, 0.3, 1) if app.autopilot else (0, 0.8, 0, 1)
                    on_press: root.toggle_autopilot()
                    size_hint_y: None
                    height: 50
                    canvas.before:
                        Color:
                            rgba: self.background_color
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]

            # Heading Control
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: heading_control_title
                    text: app.get_translation('HEADING CONTROL') if app else 'HEADING CONTROL'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Label:
                    text: '270°'
                    font_size: '36sp'
                    color: 0.9, 0.95, 1, 1
                    disabled: not app.autopilot

                BoxLayout:
                    orientation: 'horizontal'
                    spacing: 20
                    size_hint_y: None
                    height: 50
                    
                    Button:
                        text: '-1°'
                        font_size: '18sp'
                        disabled: not app.autopilot
                        on_press: root.adjust_heading(-1)
                        size_hint_x: 0.25
                        
                    Button:
                        text: '-10°'
                        font_size: '18sp'
                        disabled: not app.autopilot
                        on_press: root.adjust_heading(-10)
                        size_hint_x: 0.25
                        
                    Button:
                        text: '+10°'
                        font_size: '18sp'
                        disabled: not app.autopilot
                        on_press: root.adjust_heading(10)
                        size_hint_x: 0.25
                        
                    Button:
                        text: '+1°'
                        font_size: '18sp'
                        disabled: not app.autopilot
                        on_press: root.adjust_heading(1)
                        size_hint_x: 0.25

        # Navigation Data
        BoxLayout:
            size_hint: 0.6, 0.15
            pos_hint: {'center_x': 0.5, 'center_y': 0.15}
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]

            BoxLayout:
                orientation: 'vertical'
                padding: 10
                
                Label:
                    id: navigation_data_title
                    text: app.get_translation('NAVIGATION DATA') if app else 'NAVIGATION DATA'
                    font_size: '16sp'
                
                GridLayout:
                    cols: 3
                    spacing: 5
                    
                    Label:
                        id: current_heading_label
                        text: app.get_translation('Current Heading:') if app else 'Current Heading:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: '270°'
                        font_size: '14sp'
                        halign: 'center'
                        
                    Widget:
                        size_hint_x: 0.1
                    
                    Label:
                        id: speed_label
                        text: app.get_translation('Speed:') if app else 'Speed:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: '8.5 knots'
                        font_size: '14sp'
                        halign: 'center'
                        
                    Widget:
                        size_hint_x: 0.1
                    
                    Label:
                        id: gps_position_label
                        text: app.get_translation('GPS Position:') if app else 'GPS Position:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: '54°22.7′N 18°38.2′E'
                        font_size: '14sp'
                        halign: 'center'
                        
                    Widget:
                        size_hint_x: 0.1
