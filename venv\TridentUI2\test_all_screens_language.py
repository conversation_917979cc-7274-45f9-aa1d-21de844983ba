#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test for All Screens Language Support
Tests if all screens in TridentOS support global language changes
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_screens_inheritance():
    """Test if all screens inherit from TranslatableScreen"""
    print("🧪 Testing All Screens Inheritance")
    print("=" * 50)
    
    try:
        import main
        from translatable_screen import TranslatableScreen
        
        # List of all screen classes that should inherit from TranslatableScreen
        screen_classes = [
            ('HomeScreen', main.HomeScreen),
            ('ClimateScreen', main.ClimateScreen),
            ('LightningScreen', main.LightningScreen),
            ('BatteryScreen', main.BatteryScreen),
            ('EngineScreen', main.EngineScreen),
            ('WaterScreen', main.WaterScreen),
            ('FuelScreen', main.FuelScreen),
            ('AutopilotScreen', main.AutopilotScreen),
            ('SettingsScreen', main.SettingsScreen),
            ('AlarmScreen', main.AlarmScreen)
        ]
        
        all_passed = True
        
        for screen_name, screen_class in screen_classes:
            is_translatable = issubclass(screen_class, TranslatableScreen)
            status = "✅" if is_translatable else "❌"
            print(f"   {status} {screen_name}: {'TranslatableScreen' if is_translatable else 'Screen'}")
            
            if not is_translatable:
                all_passed = False
        
        print(f"\n📊 Result: {'✅ All screens inherit from TranslatableScreen' if all_passed else '❌ Some screens still inherit from Screen'}")
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing screen inheritance: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_creation_and_language_change():
    """Test creating screens and changing language"""
    print("\n🧪 Testing Screen Creation and Language Change")
    print("=" * 50)
    
    try:
        import main
        from language_manager import set_global_language, get_global_language
        
        # Test creating each screen
        screens_to_test = [
            ('HomeScreen', main.HomeScreen),
            ('SettingsScreen', main.SettingsScreen),
            ('LightningScreen', main.LightningScreen),
            ('EngineScreen', main.EngineScreen)
        ]
        
        created_screens = []
        
        for screen_name, screen_class in screens_to_test:
            try:
                print(f"\n   🔧 Creating {screen_name}...")
                screen = screen_class(name=f"test_{screen_name.lower()}")
                created_screens.append((screen_name, screen))
                print(f"   ✅ {screen_name} created successfully")
                print(f"   📝 Current language: {screen.current_language}")
                
                # Test setup_translations if available
                if hasattr(screen, 'setup_translations'):
                    screen.setup_translations()
                    print(f"   🌍 Translations setup completed")
                
            except Exception as e:
                print(f"   ❌ Error creating {screen_name}: {e}")
        
        # Test language change on all created screens
        print(f"\n🔄 Testing language change on {len(created_screens)} screens...")
        
        test_languages = ["Polski", "Deutsch", "English"]
        
        for language in test_languages:
            print(f"\n   🌍 Changing to {language}...")
            success = set_global_language(language)
            
            if success:
                current_global = get_global_language()
                print(f"   📍 Global language: {current_global}")
                
                # Check if all screens received the language change
                for screen_name, screen in created_screens:
                    screen_lang = getattr(screen, 'current_language', 'Unknown')
                    status = "✅" if screen_lang == language else "❌"
                    print(f"     {status} {screen_name}: {screen_lang}")
            else:
                print(f"   ❌ Failed to set global language to {language}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing screen creation and language change: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_keys():
    """Test if translation keys are available"""
    print("\n🧪 Testing Translation Keys")
    print("=" * 50)
    
    try:
        from translatable_screen import tr
        from language_manager import set_global_language
        
        # Test key translation keys
        test_keys = [
            "HOME", "SETTINGS", "ENGINE", "LIGHTNING", "BATTERY",
            "NAVIGATION LIGHTNING", "INTERIOR LIGHTNING", "DECK LIGHTNING",
            "ENGINE CONTROL", "ENGINE STATUS", "THROTTLE CONTROL",
            "START", "STOP", "Red", "Green", "Blue", "Intensity"
        ]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        for language in languages:
            print(f"\n   🌍 Testing {language} translations...")
            set_global_language(language)
            
            for key in test_keys:
                translation = tr(key)
                # Check if translation is different from key (indicating it was translated)
                is_translated = translation != key or language == "English"
                status = "✅" if is_translated else "⚠️"
                print(f"     {status} {key}: {translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing translation keys: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_global_language_propagation():
    """Test if language changes propagate to all screens"""
    print("\n🧪 Testing Global Language Propagation")
    print("=" * 50)
    
    try:
        import main
        from language_manager import set_global_language, register_global_ui_callback
        
        # Create multiple screens
        screens = [
            main.HomeScreen(name="test_home"),
            main.SettingsScreen(name="test_settings"),
            main.LightningScreen(name="test_lightning"),
            main.EngineScreen(name="test_engine")
        ]
        
        print(f"   📱 Created {len(screens)} test screens")
        
        # Track language changes
        language_changes = []
        
        def track_language_change(language):
            language_changes.append(language)
            print(f"   📢 Global language change detected: {language}")
        
        register_global_ui_callback(track_language_change)
        
        # Test language changes
        test_languages = ["Polski", "Deutsch", "Русский", "English"]
        
        for language in test_languages:
            print(f"\n   🔄 Setting global language to {language}...")
            language_changes.clear()
            
            success = set_global_language(language)
            
            if success:
                print(f"   ✅ Global language set successfully")
                print(f"   📊 Callbacks triggered: {len(language_changes)}")
                
                # Check all screens
                all_updated = True
                for i, screen in enumerate(screens):
                    screen_lang = getattr(screen, 'current_language', 'Unknown')
                    if screen_lang == language:
                        print(f"     ✅ Screen {i+1}: {screen_lang}")
                    else:
                        print(f"     ❌ Screen {i+1}: {screen_lang} (expected {language})")
                        all_updated = False
                
                if all_updated:
                    print(f"   🎉 All screens updated to {language}")
                else:
                    print(f"   ⚠️ Some screens not updated properly")
            else:
                print(f"   ❌ Failed to set global language")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing global language propagation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Starting All Screens Language Support Tests")
    print("=" * 60)
    
    tests = [
        test_all_screens_inheritance,
        test_screen_creation_and_language_change,
        test_translation_keys,
        test_global_language_propagation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        
        print("-" * 60)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! All screens support global language changes!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
