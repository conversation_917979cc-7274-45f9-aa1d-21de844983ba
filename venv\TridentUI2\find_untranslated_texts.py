#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Find Untranslated Texts in TridentOS
Scans all .kv files to find texts that are not using app.get_translation()
"""

import os
import re

def find_untranslated_texts():
    """Find all untranslated texts in .kv files"""
    print("🔍 Scanning for untranslated texts in .kv files")
    print("=" * 60)
    
    kv_files = [
        'ui/home.kv', 'ui/settings.kv', 'ui/lightning.kv', 'ui/engine.kv',
        'ui/alarm.kv', 'ui/autopilot.kv', 'ui/battery.kv', 'ui/climate.kv',
        'ui/fuel.kv', 'ui/water.kv'
    ]
    
    # Pattern to find text properties that are not using app.get_translation
    # Matches: text: "something" or text: 'something' but not app.get_translation
    text_pattern = r'text:\s*["\']([^"\']+)["\']'
    translation_pattern = r'app\.get_translation'
    
    all_untranslated = {}
    total_texts = 0
    total_translated = 0
    
    for file_path in kv_files:
        if not os.path.exists(file_path):
            print(f"⚠️ File not found: {file_path}")
            continue
            
        print(f"\n📄 Analyzing {file_path}...")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        untranslated_in_file = []
        translated_count = 0
        
        for i, line in enumerate(lines, 1):
            # Skip comments
            if line.strip().startswith('#'):
                continue
                
            # Check if line has text property
            text_match = re.search(text_pattern, line)
            if text_match:
                text_value = text_match.group(1)
                total_texts += 1
                
                # Check if this line uses app.get_translation
                if re.search(translation_pattern, line):
                    translated_count += 1
                    total_translated += 1
                else:
                    # Skip certain patterns that shouldn't be translated
                    skip_patterns = [
                        r'^\d+$',  # Pure numbers
                        r'^\d+%$',  # Percentages
                        r'^\d+°$',  # Degrees
                        r'^\d+\.\d+$',  # Decimals
                        r'^[+-]\d+°?$',  # +/- numbers with optional degree
                        r'^\w+\.(png|jpg|jpeg|gif)$',  # Image files
                        r'^icons/',  # Icon paths
                        r'^\s*$',  # Empty or whitespace
                        r'^[A-Z]{1,3}$',  # Short abbreviations like RPM, GPS
                        r'^\d+:\d+$',  # Time format
                        r'^\d+\.\d+\s*(knots|nm|kW|L/h)$',  # Values with units
                    ]
                    
                    should_skip = False
                    for pattern in skip_patterns:
                        if re.match(pattern, text_value.strip()):
                            should_skip = True
                            break
                    
                    if not should_skip:
                        untranslated_in_file.append({
                            'line': i,
                            'text': text_value,
                            'full_line': line.strip()
                        })
        
        if untranslated_in_file:
            all_untranslated[file_path] = untranslated_in_file
            print(f"  ❌ Found {len(untranslated_in_file)} untranslated texts")
            for item in untranslated_in_file[:5]:  # Show first 5
                print(f"    Line {item['line']}: '{item['text']}'")
            if len(untranslated_in_file) > 5:
                print(f"    ... and {len(untranslated_in_file) - 5} more")
        else:
            print(f"  ✅ All texts are translated")
        
        print(f"  📊 Translated: {translated_count}, Total texts: {len([l for l in lines if re.search(text_pattern, l)])}")
    
    # Summary
    print(f"\n📊 SUMMARY")
    print("=" * 60)
    print(f"Total text properties found: {total_texts}")
    print(f"Total translated: {total_translated}")
    print(f"Total untranslated: {total_texts - total_translated}")
    print(f"Translation coverage: {(total_translated/total_texts*100):.1f}%" if total_texts > 0 else "N/A")
    
    # Detailed report
    if all_untranslated:
        print(f"\n🔍 DETAILED UNTRANSLATED TEXTS")
        print("=" * 60)
        
        for file_path, items in all_untranslated.items():
            print(f"\n📄 {file_path} ({len(items)} untranslated):")
            for item in items:
                print(f"  Line {item['line']:3d}: '{item['text']}'")
                print(f"           {item['full_line']}")
    
    return all_untranslated

def suggest_translations():
    """Suggest translation keys for untranslated texts"""
    print(f"\n💡 SUGGESTED TRANSLATION ADDITIONS")
    print("=" * 60)
    
    untranslated = find_untranslated_texts()
    
    if not untranslated:
        print("✅ No untranslated texts found!")
        return
    
    # Collect all unique untranslated texts
    unique_texts = set()
    for file_items in untranslated.values():
        for item in file_items:
            unique_texts.add(item['text'])
    
    print(f"\n📝 Add these keys to translations.py:")
    print("# Additional translations needed")
    for text in sorted(unique_texts):
        # Clean up the text for use as a key
        key = text.strip()
        print(f'"{key}": "{key}",')
    
    print(f"\n🌍 Then add translations for each language:")
    print("Polski, Deutsch, Русский versions needed for each key above.")

def main():
    """Main function"""
    print("🔍 TridentOS Untranslated Text Finder")
    print("=" * 60)
    
    try:
        untranslated = find_untranslated_texts()
        
        if untranslated:
            suggest_translations()
            print(f"\n⚠️ Found untranslated texts in {len(untranslated)} files")
            print("Run this script to identify what needs translation.")
        else:
            print("\n🎉 All texts appear to be translated!")
            print("Great job on the translation coverage!")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
