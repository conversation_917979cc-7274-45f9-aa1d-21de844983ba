#:kivy 2.0.0

<WaterScreen>:
    FloatLayout:
        canvas.before:
            Color:
                rgba: 0.05, 0.1, 0.15, 1  # Dark blue background
            Rectangle:
                pos: self.pos
                size: self.size

        # Header with back button and icons
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 0, 0]
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            Widget:
                size_hint_x: 0.8  # empty space in the middle
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # WATER title
        Label:
            id: water_title
            text: app.get_translation('WATER') if app else 'WATER'
            font_size: '24sp'
            size_hint: None, None
            size: self.texture_size
            pos_hint: {'center_x': 0.5, 'top': 0.92}

        # Clock
        ClockWidget:
            id: clock
            font_size: '30sp'
            pos_hint: {'center_x': 0.5, 'top': 0.86}
            size_hint: None, None

        # Water Level Display
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.8, 0.25
            pos_hint: {'center_x': 0.5, 'top': 0.8}
            spacing: 10

            Label:
                id: water_level_title
                text: app.get_translation('WATER LEVEL') if app else 'WATER LEVEL'
                font_size: '20sp'
                size_hint_y: None
                height: 30

            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 80
                spacing: 20

                # Percentage display
                Label:
                    id: water_level_label
                    text: f"{int(app.water_level)}%"
                    font_size: '48sp'
                    size_hint_x: 0.3

                # Progress bar
                BoxLayout:
                    orientation: 'vertical'
                    size_hint_x: 0.7
                    padding: [0, 20, 0, 20]

                    ProgressBar:
                        id: water_level_bar
                        value: app.water_level
                        max: 100
                        size_hint_y: None
                        height: 40
                        background_color: 0.1, 0.1, 0.1, 1
                        color: 0.2, 0.6, 0.8, 1

        # Water System Controls
        GridLayout:
            cols: 2
            size_hint: 0.9, 0.35
            pos_hint: {'center_x': 0.5, 'center_y': 0.4}
            spacing: 15
            padding: 10

            # Fresh Water Tank
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: fresh_water_tank_title
                    text: app.get_translation('FRESH WATER TANK') if app else 'FRESH WATER TANK'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'icons/water.png'
                    size_hint_y: None
                    height: 48
                    pos_hint: {'center_x': 0.5}

                Label:
                    text: f"{int(app.water_level)}%"
                    font_size: '28sp'
                    color: 0.9, 0.95, 1, 1

                Label:
                    text: f"{int(app.water_level * 2)} liters remaining"
                    font_size: '14sp'
                    color: 0.7, 0.75, 0.8, 1

            # Water Pump Status
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: water_pump_title
                    text: app.get_translation('WATER PUMP') if app else 'WATER PUMP'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'icons/pump.png'
                    size_hint_y: None
                    height: 48
                    pos_hint: {'center_x': 0.5}

                Button:
                    id: water_pump_btn
                    text: app.get_translation('ON') if app else 'ON'
                    font_size: '22sp'
                    background_color: 0, 0.8, 0, 1
                    size_hint_y: None
                    height: 50
                    on_press: root.toggle_water_pump()
                    canvas.before:
                        Color:
                            rgba: self.background_color
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]

        # Water Usage Statistics
        BoxLayout:
            size_hint: 0.5, 0.15
            pos_hint: {'center_x': 0.5, 'center_y': 0.15}
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]

            BoxLayout:
                orientation: 'vertical'
                padding: 10
                
                Label:
                    id: water_usage_stats_title
                    text: app.get_translation('WATER USAGE STATISTICS') if app else 'WATER USAGE STATISTICS'
                    font_size: '16sp'
                
                GridLayout:
                    cols: 2
                    spacing: 5
                    
                    Label:
                        id: daily_usage_label
                        text: app.get_translation('Daily Usage:') if app else 'Daily Usage:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: app.get_translation('25 liters') if app else '25 liters'
                        font_size: '14sp'
                        halign: 'left'
                    
                    Label:
                        id: estimated_days_label
                        text: app.get_translation('Estimated Days Left:') if app else 'Estimated Days Left:'
                        font_size: '14sp'
                        halign: 'right'
                    
                    Label:
                        text: f"{int(app.water_level * 2 / 25)}" if app.water_level > 0 else "0"
                        font_size: '14sp'
                        halign: 'left'
