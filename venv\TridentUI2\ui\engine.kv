#:kivy 2.0.0

<EngineScreen>:
    FloatLayout:
        canvas.before:
            Color:
                rgba: 0.05, 0.1, 0.15, 1  # Dark blue background
            Rectangle:
                pos: self.pos
                size: self.size

        # Header with back button and icons
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 0, 0]
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            Widget:
                size_hint_x: 0.8  # empty space in the middle
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # ENGINE title
        Label:
            id: engine_title
            text: app.get_translation('ENGINE CONTROL') if app else 'ENGINE CONTROL'
            font_size: '28sp'
            size_hint: None, None
            size: self.texture_size
            pos_hint: {'center_x': 0.5, 'top': 0.92}
            color: 0.75, 0.85, 1, 1

        # Clock
        ClockWidget:
            id: clock
            font_size: '24sp'
            pos_hint: {'center_x': 0.5, 'top': 0.86}
            size_hint: None, None

        # Engine Status Display
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.9, 0.15
            pos_hint: {'center_x': 0.5, 'top': 0.8}
            spacing: 10
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]
            padding: 15

            BoxLayout:
                orientation: 'horizontal'
                spacing: 20

                # Status indicator
                BoxLayout:
                    orientation: 'vertical'
                    size_hint_x: 0.6

                    Label:
                        id: engine_status_title
                        text: app.get_translation('ENGINE STATUS') if app else 'ENGINE STATUS'
                        font_size: '18sp'
                        size_hint_y: None
                        height: 30
                        color: 0.75, 0.85, 1, 1

                    Label:
                        id: engine_status_label
                        text: app.engine_status.upper()
                        font_size: '36sp'
                        color: (0, 0.8, 0, 1) if app.engine_status == "active" else (0.8, 0.3, 0.3, 1)

                # Engine image and start/stop button
                BoxLayout:
                    orientation: 'vertical'
                    size_hint_x: 0.4
                    spacing: 5

                    Image:
                        source: 'icons/engine.png'
                        size_hint_y: None
                        height: 48
                        pos_hint: {'center_x': 0.5}

                    Button:
                        text: (app.get_translation('START') if app.engine_status != 'active' else app.get_translation('STOP')) if app else ('START' if app.engine_status != 'active' else 'STOP')
                        font_size: '20sp'
                        background_color: (0, 0.8, 0, 1) if app.engine_status != "active" else (0.8, 0.3, 0.3, 1)
                        on_press: root.toggle_engine()
                        size_hint_y: None
                        height: 40
                        canvas.before:
                            Color:
                                rgba: self.background_color
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [10]

        # Throttle Control
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.9, 0.2
            pos_hint: {'center_x': 0.5, 'top': 0.62}
            spacing: 10
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]
            padding: 15

            Label:
                id: throttle_title
                text: app.get_translation('THROTTLE CONTROL') if app else 'THROTTLE CONTROL'
                font_size: '18sp'
                size_hint_y: None
                height: 30
                color: 0.75, 0.85, 1, 1

            BoxLayout:
                orientation: 'horizontal'
                spacing: 15

                # Throttle slider
                Slider:
                    id: throttle_slider
                    min: 0
                    max: 100
                    value: app.engine_throttle
                    step: 1
                    orientation: 'horizontal'
                    disabled: app.engine_status != "active"
                    on_value: root.set_throttle(self.value) if app.engine_status == "active" else None
                    size_hint_x: 0.7
                    cursor_size: (30, 30)
                    background_width: 8
                    background_color: (0.2, 0.2, 0.2, 1)
                    cursor_color: (0, 0.8, 0, 1) if app.engine_status == "active" else (0.5, 0.5, 0.5, 1)

                # Throttle value display
                Label:
                    text: f"{int(throttle_slider.value)}%"
                    font_size: '24sp'
                    size_hint_x: 0.3
                    color: (0.3, 0.8, 0.3, 1) if app.engine_status == "active" else (0.5, 0.5, 0.5, 1)

        # Engine Parameters
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.9, 0.25
            pos_hint: {'center_x': 0.5, 'center_y': 0.3}
            spacing: 10
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]
            padding: 15

            Label:
                id: engine_params_title
                text: app.get_translation('ENGINE PARAMETERS') if app else 'ENGINE PARAMETERS'
                font_size: '18sp'
                size_hint_y: None
                height: 30
                color: 0.75, 0.85, 1, 1

            GridLayout:
                cols: 2
                spacing: 10

                Label:
                    text: app.get_translation('RPM:') if app else 'RPM:'
                    font_size: '18sp'
                    halign: 'right'
                    color: 0.75, 0.85, 1, 1

                Label:
                    id: rpm_label
                    text: '0'
                    font_size: '18sp'
                    halign: 'left'
                    color: (0.3, 0.8, 0.3, 1) if app.engine_status == "active" else (0.5, 0.5, 0.5, 1)

                Label:
                    text: app.get_translation('Temperature:') if app else 'Temperature:'
                    font_size: '18sp'
                    halign: 'right'
                    color: 0.75, 0.85, 1, 1

                Label:
                    id: temp_label
                    text: f"{app.engine_temperature:.1f}°C"
                    font_size: '18sp'
                    halign: 'left'
                    color: (0.3, 0.8, 0.3, 1) if app.engine_status == "active" else (0.5, 0.5, 0.5, 1)

                Label:
                    text: app.get_translation('Oil Pressure:') if app else 'Oil Pressure:'
                    font_size: '18sp'
                    halign: 'right'
                    color: 0.75, 0.85, 1, 1

                Label:
                    id: oil_pressure_label
                    text: app.get_translation('0.0 bar') if app else '0.0 bar'
                    font_size: '18sp'
                    halign: 'left'
                    color: (0.3, 0.8, 0.3, 1) if app.engine_status == "active" else (0.5, 0.5, 0.5, 1)

                Label:
                    text: app.get_translation('Engine Hours:') if app else 'Engine Hours:'
                    font_size: '18sp'
                    halign: 'right'
                    color: 0.75, 0.85, 1, 1

                Label:
                    id: hours_label
                    text: f"{int(app.engine_hours)} h"
                    font_size: '18sp'
                    halign: 'left'
                    color: 0.75, 0.85, 1, 1

        # System Status
        BoxLayout:
            size_hint: 0.3, 0.08
            pos_hint: {'center_x': 0.5, 'center_y': 0.08}
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]

            BoxLayout:
                orientation: 'vertical'
                padding: 5
                Label:
                    text: app.get_translation('SYSTEM STATUS') if app else 'SYSTEM STATUS'
                    font_size: '14sp'
                    color: 0.75, 0.85, 1, 1
                Label:
                    id: system_status
                    text: 'OK'
                    color: 0, 1, 0, 1  # Green color for OK status
