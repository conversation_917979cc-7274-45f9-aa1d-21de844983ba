#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Translation System Test
Complete test of all translations and language switching
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_language_switching():
    """Test switching between all languages"""
    print("🌍 Testing Language Switching")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language, get_global_language
        from translatable_screen import tr
        
        # Test key translations for each language
        test_keys = [
            "HOME", "SETTINGS", "BATTERY", "WATER", "FUEL", "CLIMATE", 
            "AUTOPILOT", "ENGINE", "LIGHTNING", "ALARM",
            "on", "off", "active", "inactive", "START", "STOP",
            "WiFi", "Bluetooth", "Connect", "Password:",
            "Temperature:", "RPM:", "Oil Pressure:",
            "Brightness", "Language", "System"
        ]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        for language in languages:
            print(f"\n🔄 Testing {language}:")
            success = set_global_language(language)
            
            if success:
                current = get_global_language()
                print(f"  ✅ Language set to: {current}")
                
                # Test some key translations
                sample_translations = []
                for key in test_keys[:8]:  # Test first 8 keys
                    translation = tr(key)
                    sample_translations.append(f"{key} → {translation}")
                
                print(f"  📝 Sample translations:")
                for trans in sample_translations[:4]:  # Show first 4
                    print(f"    {trans}")
                
                # Check if translations are working (different from English for non-English languages)
                working_translations = 0
                for key in test_keys[:8]:
                    translation = tr(key)
                    if language == "English" or translation != key:
                        working_translations += 1
                
                coverage = (working_translations / 8) * 100
                print(f"  📊 Translation coverage: {coverage:.1f}%")
                
                if coverage >= 75:
                    print(f"  ✅ {language} translations working well!")
                else:
                    print(f"  ⚠️ {language} translations need improvement")
            else:
                print(f"  ❌ Failed to set language to {language}")
        
        return True
        
    except Exception as e:
        print(f"❌ Language switching test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_coverage():
    """Test GUI translation coverage"""
    print("\n🖥️ Testing GUI Translation Coverage")
    print("=" * 50)
    
    try:
        # Count total translation calls in .kv files
        kv_files = [
            'ui/home.kv', 'ui/settings.kv', 'ui/lightning.kv', 'ui/engine.kv',
            'ui/alarm.kv', 'ui/autopilot.kv', 'ui/battery.kv', 'ui/climate.kv',
            'ui/fuel.kv', 'ui/water.kv'
        ]
        
        total_translation_calls = 0
        files_with_translations = 0
        
        for file_path in kv_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                translation_calls = content.count('app.get_translation')
                total_translation_calls += translation_calls
                
                if translation_calls > 0:
                    files_with_translations += 1
                    print(f"  ✅ {file_path}: {translation_calls} translations")
                else:
                    print(f"  ⚠️ {file_path}: No translations")
        
        print(f"\n📊 GUI Translation Summary:")
        print(f"  Files with translations: {files_with_translations}/{len(kv_files)}")
        print(f"  Total translation calls: {total_translation_calls}")
        
        if files_with_translations >= 8 and total_translation_calls >= 80:
            print("  🎉 Excellent GUI translation coverage!")
            return True
        elif files_with_translations >= 6 and total_translation_calls >= 50:
            print("  ✅ Good GUI translation coverage!")
            return True
        else:
            print("  ⚠️ GUI translation coverage needs improvement")
            return False
            
    except Exception as e:
        print(f"❌ GUI coverage test failed: {e}")
        return False

def test_specific_screens():
    """Test specific screen translations"""
    print("\n📱 Testing Specific Screen Translations")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Test specific important translations
        important_translations = {
            "Settings Screen": ["SETTINGS", "DISPLAY", "LANGUAGE", "SYSTEM", "WiFi", "Bluetooth"],
            "Engine Screen": ["ENGINE", "START", "STOP", "RPM:", "Temperature:", "Oil Pressure:"],
            "Battery Screen": ["BATTERY", "BATTERY LEVEL", "POWER CONSUMPTION", "CHARGING", "DISCHARGING"],
            "Climate Screen": ["CLIMATE", "SET TEMPERATURE", "FAN POWER", "AUTOMATIC AC"],
            "Autopilot Screen": ["AUTOPILOT", "AUTOPILOT STATUS", "HEADING CONTROL", "NAVIGATION DATA"],
            "Alarm Screen": ["ALARM STATUS", "SYSTEM STATUS", "Critical", "Warning", "Information"]
        }
        
        # Test in Polish (most comprehensive translations)
        set_global_language("Polski")
        
        for screen_name, keys in important_translations.items():
            print(f"\n  🖥️ {screen_name}:")
            translated_count = 0
            
            for key in keys:
                translation = tr(key)
                is_translated = translation != key  # Different from English = translated
                
                if is_translated:
                    translated_count += 1
                    print(f"    ✅ {key} → {translation}")
                else:
                    print(f"    ❌ {key} → {translation} (not translated)")
            
            coverage = (translated_count / len(keys)) * 100
            print(f"    📊 Coverage: {coverage:.1f}% ({translated_count}/{len(keys)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Specific screens test failed: {e}")
        return False

def test_button_functionality():
    """Test if language buttons work"""
    print("\n🔘 Testing Language Button Functionality")
    print("=" * 50)
    
    try:
        # Test if we can import and use the settings screen methods
        import main
        
        # Create a mock settings screen
        settings = main.SettingsScreen()
        
        # Test each language button method
        language_methods = [
            ("English", settings.set_language_english),
            ("Polski", settings.set_language_polish),
            ("Deutsch", settings.set_language_german),
            ("Русский", settings.set_language_russian)
        ]
        
        working_buttons = 0
        
        for lang_name, method in language_methods:
            try:
                print(f"  🔄 Testing {lang_name} button...")
                result = method()
                
                if result is not False:  # Method executed without returning False
                    working_buttons += 1
                    print(f"    ✅ {lang_name} button works")
                else:
                    print(f"    ❌ {lang_name} button failed")
                    
            except Exception as e:
                print(f"    ❌ {lang_name} button error: {e}")
        
        print(f"\n📊 Button Functionality: {working_buttons}/{len(language_methods)} working")
        
        return working_buttons >= 3  # At least 3 out of 4 should work
        
    except Exception as e:
        print(f"❌ Button functionality test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Final Translation System Test")
    print("=" * 70)
    print("Testing complete translation system functionality...")
    
    tests = [
        ("Language Switching", test_language_switching),
        ("GUI Coverage", test_gui_coverage),
        ("Specific Screens", test_specific_screens),
        ("Button Functionality", test_button_functionality)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*70}")
        print(f"🧪 Running: {test_name}")
        print(f"{'='*70}")
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    # Final results
    print(f"\n{'='*70}")
    print(f"📊 FINAL RESULTS")
    print(f"{'='*70}")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 PERFECT! All translation tests passed!")
        print("🌍 Complete translation system is fully functional!")
        print("✨ Users can now switch between 4 languages seamlessly!")
        return True
    elif passed >= total * 0.75:  # 75% success rate
        print("✅ EXCELLENT! Most translation tests passed!")
        print("🌍 Translation system is working very well!")
        print("🔧 Minor issues may need attention.")
        return True
    else:
        print("⚠️ Some translation tests failed.")
        print("🔧 System needs more work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
