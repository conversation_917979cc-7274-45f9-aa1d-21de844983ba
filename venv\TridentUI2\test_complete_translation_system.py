#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Translation System Test
Tests all screens and all translations in TridentOS
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_all_translation_keys():
    """Test all translation keys across all languages"""
    print("🧪 Testing All Translation Keys")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Comprehensive list of all translation keys
        all_keys = [
            # Basic navigation
            "HOME", "SETTINGS", "ENGINE", "LIGHTNING", "BATTERY", "WATER", "FUEL", 
            "AUTOPILOT", "CLIMATE", "ALARM",
            
            # Status values
            "on", "off", "active", "inactive", "OK", "ALARM", "WARNING", "CRITICAL",
            "ACTIVE", "INACTIVE", "CHARGING", "DISCHARGING",
            
            # Alarm screen
            "ALARM STATUS", "SYSTEM STATUS", "ALERT", "Critical", "Warning", "Information",
            "Acknowledge All", "Add Test Alarm", "Go to Home", "ACKNOWLEDGED", "UNACKNOWLEDGED",
            
            # Autopilot screen
            "AUTOPILOT", "AUTOPILOT STATUS", "AUTOPILOT CONTROL", "DEACTIVATE", "ACTIVATE",
            "HEADING CONTROL", "NAVIGATION DATA", "Current Heading:", "Speed:", "GPS Position:",
            
            # Battery screen
            "BATTERY", "BATTERY LEVEL", "POWER CONSUMPTION", "Current usage", "BATTERY STATUS",
            "POWER SOURCE", "Battery", "Shore Power", "Solar Panels",
            
            # Climate screen
            "CLIMATE", "target temp", "current temp", "external temp", "SET TEMPERATURE",
            "FAN POWER", "AUTOMATIC AC", "SET FRIDGE TEMPERATURE", "HEATING", "COOLING",
            
            # Fuel screen
            "FUEL", "FUEL LEVEL", "FUEL TANK", "FUEL CONSUMPTION", "Current consumption",
            "RANGE ESTIMATION", "Estimated Range:", "Estimated Distance:",
            
            # Water screen
            "WATER", "WATER LEVEL", "FRESH WATER TANK", "WATER PUMP", "WATER USAGE STATISTICS",
            "Daily Usage:", "Estimated Days Left:",
            
            # Settings
            "DISPLAY", "LANGUAGE", "SYSTEM", "SAFETY", "CONNECTION", "CALIBRATION",
            "Brightness", "Theme Mode", "Screen Orientation", "Time Format", "Units",
            "Restart System"
        ]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        total_tests = 0
        passed_tests = 0
        
        for language in languages:
            print(f"\n🌍 Testing {language}:")
            set_global_language(language)
            
            for key in all_keys[:20]:  # Test first 20 keys for each language
                translation = tr(key)
                total_tests += 1
                
                # Check if translation is different from key (indicating it was translated)
                # or if it's English (where translation equals key)
                is_translated = translation != key or language == "English"
                
                if is_translated:
                    passed_tests += 1
                    status = "✅"
                else:
                    status = "❌"
                
                print(f"  {status} {key}: {translation}")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n📊 Translation Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")
        
        return success_rate > 95  # Consider success if >95% translations work
        
    except Exception as e:
        print(f"❌ Translation keys test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_kv_files_updated():
    """Test if all .kv files have been updated with translations"""
    print("\n🧪 Testing All KV Files Updates")
    print("=" * 50)
    
    try:
        kv_files = [
            'ui/home.kv', 'ui/settings.kv', 'ui/lightning.kv', 'ui/engine.kv',
            'ui/alarm.kv', 'ui/autopilot.kv', 'ui/battery.kv', 'ui/climate.kv',
            'ui/fuel.kv', 'ui/water.kv'
        ]
        
        total_files = len(kv_files)
        updated_files = 0
        total_translations = 0
        
        for file_path in kv_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                translation_calls = content.count('app.get_translation')
                total_translations += translation_calls
                
                if translation_calls > 0:
                    updated_files += 1
                    print(f"  ✅ {file_path}: {translation_calls} translations")
                else:
                    print(f"  ❌ {file_path}: No translations found")
            else:
                print(f"  ⚠️ {file_path}: File not found")
        
        print(f"\n📊 Files Updated: {updated_files}/{total_files}")
        print(f"📊 Total Translation Calls: {total_translations}")
        
        return updated_files >= 8  # At least 8 out of 10 files should be updated
        
    except Exception as e:
        print(f"❌ KV files test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_screen_mappings():
    """Test if all screens have translation mappings"""
    print("\n🧪 Testing All Screen Mappings")
    print("=" * 50)
    
    try:
        import main
        
        # Create app instance
        app = main.MainApp()
        
        # Test screens
        screen_names = ['home', 'settings', 'lightning', 'engine', 'alarm', 
                       'autopilot', 'battery', 'climate', 'fuel', 'water']
        
        screens_with_mappings = 0
        
        for screen_name in screen_names:
            # Create mock screen
            class MockScreen:
                def __init__(self):
                    self.ids = {}
                    # Add a few mock widgets
                    for i in range(3):
                        mock_widget = MockWidget()
                        self.ids[f'test_widget_{i}'] = mock_widget
            
            class MockWidget:
                def __init__(self):
                    self.text = "Original Text"
            
            mock_screen = MockScreen()
            
            try:
                # Test if screen has mappings
                app.update_screen_translations(mock_screen, screen_name)
                screens_with_mappings += 1
                print(f"  ✅ {screen_name}: Has translation mappings")
            except Exception as e:
                print(f"  ❌ {screen_name}: No mappings or error: {e}")
        
        print(f"\n📊 Screens with Mappings: {screens_with_mappings}/{len(screen_names)}")
        
        return screens_with_mappings >= 8  # At least 8 screens should have mappings
        
    except Exception as e:
        print(f"❌ Screen mappings test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_creation_with_translations():
    """Test creating screens with translation support"""
    print("\n🧪 Testing Screen Creation with Translations")
    print("=" * 50)
    
    try:
        import main
        
        # Test creating different screens
        screen_classes = [
            ('HomeScreen', main.HomeScreen),
            ('SettingsScreen', main.SettingsScreen),
            ('AlarmScreen', main.AlarmScreen),
            ('AutopilotScreen', main.AutopilotScreen),
            ('BatteryScreen', main.BatteryScreen),
            ('ClimateScreen', main.ClimateScreen),
            ('FuelScreen', main.FuelScreen),
            ('WaterScreen', main.WaterScreen)
        ]
        
        created_screens = 0
        screens_with_setup = 0
        
        for screen_name, screen_class in screen_classes:
            try:
                print(f"\n  🔧 Creating {screen_name}...")
                screen = screen_class(name=f"test_{screen_name.lower()}")
                created_screens += 1
                print(f"    ✅ {screen_name} created successfully")
                
                # Test setup_translations if available
                if hasattr(screen, 'setup_translations'):
                    screen.setup_translations()
                    screens_with_setup += 1
                    print(f"    🌍 setup_translations executed")
                else:
                    print(f"    ❌ No setup_translations method")
                
            except Exception as e:
                print(f"    ❌ Error creating {screen_name}: {e}")
        
        print(f"\n📊 Screens Created: {created_screens}/{len(screen_classes)}")
        print(f"📊 Screens with setup_translations: {screens_with_setup}/{len(screen_classes)}")
        
        return created_screens >= 6 and screens_with_setup >= 6
        
    except Exception as e:
        print(f"❌ Screen creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_global_language_propagation():
    """Test global language propagation to all screens"""
    print("\n🧪 Testing Global Language Propagation")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language, get_global_language
        import main
        
        # Create multiple screens
        screens = []
        screen_classes = [main.HomeScreen, main.SettingsScreen, main.AlarmScreen, main.BatteryScreen]
        
        for i, screen_class in enumerate(screen_classes):
            try:
                screen = screen_class(name=f"test_screen_{i}")
                screens.append(screen)
            except Exception as e:
                print(f"  ⚠️ Could not create screen {screen_class.__name__}: {e}")
        
        print(f"  📱 Created {len(screens)} test screens")
        
        # Test language changes
        test_languages = ["Polski", "Deutsch", "English"]
        successful_changes = 0
        
        for language in test_languages:
            print(f"\n  🔄 Setting global language to {language}...")
            
            success = set_global_language(language)
            
            if success:
                current_global = get_global_language()
                if current_global == language:
                    successful_changes += 1
                    print(f"    ✅ Global language set to: {current_global}")
                    
                    # Check if screens received the change
                    updated_screens = 0
                    for screen in screens:
                        screen_lang = getattr(screen, 'current_language', 'Unknown')
                        if screen_lang == language:
                            updated_screens += 1
                    
                    print(f"    📊 Screens updated: {updated_screens}/{len(screens)}")
                else:
                    print(f"    ❌ Language mismatch: expected {language}, got {current_global}")
            else:
                print(f"    ❌ Failed to set language to {language}")
        
        print(f"\n📊 Successful Language Changes: {successful_changes}/{len(test_languages)}")
        
        return successful_changes >= 2  # At least 2 out of 3 language changes should work
        
    except Exception as e:
        print(f"❌ Global language propagation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Starting Complete Translation System Tests")
    print("=" * 70)
    
    tests = [
        test_all_translation_keys,
        test_all_kv_files_updated,
        test_all_screen_mappings,
        test_screen_creation_with_translations,
        test_global_language_propagation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        
        print("-" * 70)
    
    print(f"\n📊 Final Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Complete translation system is working!")
        print("🌍 All screens and all GUI texts should now be translatable!")
        return True
    elif passed >= total * 0.8:  # 80% success rate
        print("✅ MOSTLY SUCCESSFUL! Translation system is largely working!")
        print("🌍 Most screens and GUI texts should be translatable!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
