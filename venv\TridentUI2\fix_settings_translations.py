#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Settings Screen Translation Issues
Fixes all remaining hardcoded texts in settings.kv
"""

import os

def fix_settings_translations():
    """Fix all remaining translation issues in settings.kv"""
    print("🔧 Fixing Settings Screen Translation Issues")
    print("=" * 60)
    
    file_path = 'ui/settings.kv'
    
    if not os.path.exists(file_path):
        print(f"⚠️ File not found: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    fixes_applied = 0
    
    # Define all the fixes needed
    fixes = {
        # PIN Protection ON/OFF
        "text: 'ON' if root.pin_enabled else 'OFF'": 
        "text: (app.get_translation('ON') if root.pin_enabled else app.get_translation('OFF')) if app else ('ON' if root.pin_enabled else 'OFF')",
        
        # Secure Mode ON/OFF
        "text: 'ON' if root.secure_mode else 'OFF'": 
        "text: (app.get_translation('ON') if root.secure_mode else app.get_translation('OFF')) if app else ('ON' if root.secure_mode else 'OFF')",
        
        # WiFi ON/OFF
        "text: 'ON' if root.wifi_enabled else 'OFF'": 
        "text: (app.get_translation('ON') if root.wifi_enabled else app.get_translation('OFF')) if app else ('ON' if root.wifi_enabled else 'OFF')",
        
        # Bluetooth ON/OFF
        "text: 'ON' if root.bluetooth_enabled else 'OFF'": 
        "text: (app.get_translation('ON') if root.bluetooth_enabled else app.get_translation('OFF')) if app else ('ON' if root.bluetooth_enabled else 'OFF')",
        
        # Not connected text
        "text: root.connected_network if root.connected_network else 'Not connected'": 
        "text: root.connected_network if root.connected_network else (app.get_translation('Not connected') if app else 'Not connected')",
    }
    
    # Apply each fix
    for old_text, new_text in fixes.items():
        if old_text in content:
            content = content.replace(old_text, new_text)
            fixes_applied += 1
            print(f"  ✅ Fixed: {old_text[:50]}...")
    
    # Write back if changes were made
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  📝 Applied {fixes_applied} fixes to {file_path}")
        return True
    else:
        print(f"  ℹ️ No changes needed in {file_path}")
        return False

def test_settings_translations():
    """Test if settings translations are working"""
    print("\n🧪 Testing Settings Translations")
    print("=" * 60)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Test dynamic values that should be translated
        test_values = ["Light", "Dark", "12h", "24h", "Metric", "Imperial", "ON", "OFF", "Not connected"]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        for language in languages:
            print(f"\n🔄 Testing {language}:")
            set_global_language(language)
            
            translated_count = 0
            for value in test_values:
                translation = tr(value)
                is_translated = translation != value or language == "English"
                
                if is_translated:
                    translated_count += 1
                    print(f"  ✅ {value} → {translation}")
                else:
                    print(f"  ❌ {value} → {translation} (not translated)")
            
            coverage = (translated_count / len(test_values)) * 100
            print(f"  📊 Coverage: {coverage:.1f}% ({translated_count}/{len(test_values)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings translations test failed: {e}")
        return False

def verify_settings_file():
    """Verify that settings.kv file is properly formatted"""
    print("\n🔍 Verifying Settings File")
    print("=" * 60)
    
    file_path = 'ui/settings.kv'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Count translation calls
        translation_calls = content.count('app.get_translation')
        print(f"  📊 Total translation calls: {translation_calls}")
        
        # Check for remaining hardcoded texts
        hardcoded_patterns = [
            "text: 'ON'",
            "text: 'OFF'", 
            "text: 'Not connected'",
            "text: 'Light'",
            "text: 'Dark'",
            "text: '12h'",
            "text: '24h'",
            "text: 'Metric'",
            "text: 'Imperial'"
        ]
        
        remaining_hardcoded = 0
        for pattern in hardcoded_patterns:
            if pattern in content:
                remaining_hardcoded += 1
                print(f"  ⚠️ Found hardcoded: {pattern}")
        
        if remaining_hardcoded == 0:
            print("  ✅ No hardcoded texts found!")
        else:
            print(f"  ❌ Found {remaining_hardcoded} hardcoded texts")
        
        return remaining_hardcoded == 0
        
    except Exception as e:
        print(f"❌ File verification failed: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Settings Screen Translation Fixer")
    print("=" * 70)
    
    try:
        # Step 1: Fix the translations
        fixes_applied = fix_settings_translations()
        
        # Step 2: Verify the file
        file_ok = verify_settings_file()
        
        # Step 3: Test the translations
        translations_ok = test_settings_translations()
        
        # Summary
        print(f"\n📊 Summary:")
        print(f"  Fixes applied: {'✅' if fixes_applied else '❌'}")
        print(f"  File verification: {'✅' if file_ok else '❌'}")
        print(f"  Translation test: {'✅' if translations_ok else '❌'}")
        
        if fixes_applied and file_ok and translations_ok:
            print("\n🎉 Settings screen translations fixed successfully!")
            print("🌍 All dynamic values should now be translatable!")
            return True
        elif file_ok and translations_ok:
            print("\n✅ Settings screen translations are working!")
            print("🌍 All dynamic values are translatable!")
            return True
        else:
            print("\n⚠️ Some issues remain with settings translations.")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
