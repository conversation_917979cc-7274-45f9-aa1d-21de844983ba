#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test for Settings Screen Translation
Tests if the settings screen translations work correctly
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_settings_translations():
    """Test settings screen translations"""
    print("🧪 Testing Settings Screen Translations")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language, get_global_language
        from translatable_screen import tr
        
        # Test settings-specific translation keys
        print("\n📝 Testing settings translation keys...")
        
        settings_keys = [
            "DISPLAY", "LANGUAGE", "SYSTEM", "SAFETY", "CONNECTION", "CALIBRATION",
            "Brightness", "Theme Mode", "Screen Orientation",
            "Time Format", "Units", "Restart System"
        ]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        for language in languages:
            print(f"\n🌍 Testing {language}:")
            set_global_language(language)
            
            for key in settings_keys[:6]:  # Test first 6 keys
                translation = tr(key)
                print(f"  {key}: {translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings translations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_kv_updates():
    """Test if settings.kv has been updated correctly"""
    print("\n🧪 Testing Settings KV Updates")
    print("=" * 50)
    
    try:
        # Check settings.kv
        print("\n📄 Checking settings.kv...")
        with open('ui/settings.kv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for app.get_translation usage
        translation_calls = content.count('app.get_translation')
        print(f"  Found {translation_calls} app.get_translation calls")
        
        # Check for specific IDs
        expected_ids = [
            'display_btn', 'language_btn', 'system_btn', 'safety_btn',
            'connection_btn', 'calibration_btn', 'brightness_label',
            'theme_mode_label', 'screen_orientation_label', 'language_title_label',
            'time_format_label', 'units_label', 'restart_system_btn'
        ]
        
        found_ids = 0
        for id_name in expected_ids:
            if f'id: {id_name}' in content:
                found_ids += 1
                print(f"  ✅ Found ID: {id_name}")
            else:
                print(f"  ❌ Missing ID: {id_name}")
        
        print(f"\n📊 Found {found_ids}/{len(expected_ids)} expected IDs")
        print(f"📊 Found {translation_calls} translation calls")
        
        return translation_calls > 0 and found_ids > 0
        
    except Exception as e:
        print(f"❌ Settings KV updates test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_screen_setup():
    """Test settings screen setup"""
    print("\n🧪 Testing Settings Screen Setup")
    print("=" * 50)
    
    try:
        import main
        
        # Create settings screen
        print("\n🔧 Creating SettingsScreen...")
        settings_screen = main.SettingsScreen(name="test_settings")
        print("✅ SettingsScreen created successfully")
        
        # Test setup_translations method
        if hasattr(settings_screen, 'setup_translations'):
            print("\n🌍 Testing setup_translations method...")
            settings_screen.setup_translations()
            print("✅ setup_translations method executed successfully")
        else:
            print("❌ setup_translations method not found")
            return False
        
        # Test translation mappings
        print("\n📝 Testing translation mappings...")
        app = main.MainApp()
        
        # Test update_screen_translations for settings
        if hasattr(app, 'update_screen_translations'):
            print("🔄 Testing update_screen_translations for settings...")
            app.update_screen_translations(settings_screen, 'settings')
            print("✅ update_screen_translations executed successfully")
        else:
            print("❌ update_screen_translations method not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings screen setup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_translations():
    """Test specific Polish/German/Russian translations"""
    print("\n🧪 Testing Specific Translations")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Test specific translations
        test_cases = [
            ("Polski", "DISPLAY", "WYŚWIETLACZ"),
            ("Polski", "LANGUAGE", "JĘZYK"),
            ("Polski", "Brightness", "Jasność"),
            ("Deutsch", "DISPLAY", "ANZEIGE"),
            ("Deutsch", "LANGUAGE", "SPRACHE"),
            ("Deutsch", "Brightness", "Helligkeit"),
            ("Русский", "DISPLAY", "ДИСПЛЕЙ"),
            ("Русский", "LANGUAGE", "ЯЗЫК"),
            ("Русский", "Brightness", "Яркость")
        ]
        
        for language, key, expected in test_cases:
            set_global_language(language)
            translation = tr(key)
            status = "✅" if translation == expected else "❌"
            print(f"  {status} {language} - {key}: {translation} (expected: {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ Specific translations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_translation_mappings():
    """Test app translation mappings for settings"""
    print("\n🧪 Testing App Translation Mappings")
    print("=" * 50)
    
    try:
        import main
        
        # Create app instance
        app = main.MainApp()
        
        # Check if settings mappings exist
        if hasattr(app, 'update_screen_translations'):
            print("✅ update_screen_translations method found")
            
            # Create mock screen to test mappings
            class MockScreen:
                def __init__(self):
                    self.ids = {}
                    # Add mock widgets for settings
                    settings_ids = [
                        'display_btn', 'language_btn', 'system_btn',
                        'brightness_label', 'theme_mode_label'
                    ]
                    for widget_id in settings_ids:
                        mock_widget = MockWidget()
                        self.ids[widget_id] = mock_widget
            
            class MockWidget:
                def __init__(self):
                    self.text = "Original Text"
            
            # Test settings mappings
            mock_screen = MockScreen()
            
            print("  Testing settings screen mappings...")
            app.update_screen_translations(mock_screen, 'settings')
            
            # Check if widgets were updated
            for widget_id, widget in mock_screen.ids.items():
                print(f"    {widget_id}: {widget.text}")
            
            return True
        else:
            print("❌ update_screen_translations method not found")
            return False
        
    except Exception as e:
        print(f"❌ App translation mappings test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Starting Settings Translation Tests")
    print("=" * 60)
    
    tests = [
        test_settings_translations,
        test_settings_kv_updates,
        test_settings_screen_setup,
        test_specific_translations,
        test_app_translation_mappings
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        
        print("-" * 60)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Settings screen translations should work correctly!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
