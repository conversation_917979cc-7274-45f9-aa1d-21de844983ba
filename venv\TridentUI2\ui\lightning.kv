<LightningScreen>:
    canvas.before:
        Color:
            rgba: 0.05, 0.1, 0.15, 1
        Rectangle:
            pos: self.pos
            size: self.size

    AnchorLayout:
        anchor_y: "top"
        padding: [10, 10, 10, 0]

        BoxLayout:
            orientation: "vertical"
            size_hint: 1, None
            height: self.minimum_height
            spacing: 10

            # ZEGAR NA GÓRZE
            ClockWidget:
                id: clock
                font_size: 24
                halign: "center"
                valign: "middle"
                size_hint_y: None
                height: 30

            # KAFELKI POD ZEGAREM
            BoxLayout:
                orientation: "horizontal"
                size_hint_y: None
                size_hint_x: 0.8
                pos_hint: {'center_x': 0.5}
                height: 450  # Zwiększona wysokość
                spacing: 20

                # NAVIGATION LIGHTNING
                BoxLayout:
                    orientation: "vertical"
                    padding: 10
                    size_hint: 1, 1
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.navigation_active else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 100  # <PERSON>a<PERSON> wysokość dla sekcji ikony
                        Image:
                            source: "icons/lightning.png"
                            size_hint_y: None
                            height: 64
                            pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                    Label:
                        id: navigation_label
                        text: app.get_translation("NAVIGATION LIGHTNING") if app else "NAVIGATION LIGHTNING"
                        font_size: 18
                        size_hint_y: None
                        height: 30
                    Button:
                        id: navigation_btn
                        text: app.get_translation("off") if app else "off"
                        size_hint_y: None
                        height: 40
                        pos_hint: {'center_x': 0.5}
                        on_press: root.on_navigation_light()

                # INTERIOR LIGHTNING
                BoxLayout:
                    orientation: "vertical"
                    padding: [10, 10, 10, 10]
                    spacing: 10  # Dodany spacing między elementami
                    size_hint: 1, 1
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.interior_active else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]

                    # Górna sekcja z ikoną
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 100
                        Image:
                            source: "icons/lightning.png"
                            size_hint_y: None
                            height: 64
                            pos_hint: {'center_x': 0.5}

                    # Etykieta nazwy
                    Label:
                        id: interior_label
                        text: app.get_translation("INTERIOR LIGHTNING") if app else "INTERIOR LIGHTNING"
                        font_size: 18
                        size_hint_y: None
                        height: 30

                    # Przycisk on/off
                    Button:
                        id: interior_btn
                        text: app.get_translation("off") if app else "off"
                        size_hint_y: None
                        height: 40
                        pos_hint: {'center_x': 0.5}
                        on_press: root.on_interior_light()

                    # Separator wizualny
                    Widget:
                        size_hint_y: None
                        height: 1
                        canvas:
                            Color:
                                rgba: 0.2, 0.2, 0.2, 1
                            Rectangle:
                                pos: self.pos
                                size: self.size

                    # Kontrolki RGB i intensywności
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 220
                        spacing: 2
                        opacity: 1 if root.interior_active else 0.3
                        Label:
                            id: red_label
                            text: app.get_translation("Red") if app else "Red"
                            size_hint_y: None
                            height: 25
                        Slider:
                            id: interior_r
                            size_hint_y: None
                            height: 30
                            min: 0
                            max: 255
                            value: root.interior_r
                            on_value: root.interior_r = self.value; root.update_interior_color()
                        Label:
                            id: green_label
                            text: app.get_translation("Green") if app else "Green"
                            size_hint_y: None
                            height: 25
                        Slider:
                            id: interior_g
                            size_hint_y: None
                            height: 30
                            min: 0
                            max: 255
                            value: root.interior_g
                            on_value: root.interior_g = self.value; root.update_interior_color()
                        Label:
                            id: blue_label
                            text: app.get_translation("Blue") if app else "Blue"
                            size_hint_y: None
                            height: 25
                        Slider:
                            id: interior_b
                            size_hint_y: None
                            height: 30
                            min: 0
                            max: 255
                            value: root.interior_b
                            on_value: root.interior_b = self.value; root.update_interior_color()
                        Label:
                            id: intensity_label
                            text: app.get_translation("Intensity") if app else "Intensity"
                            size_hint_y: None
                            height: 25
                        BoxLayout:
                            orientation: "horizontal"
                            size_hint_y: None
                            height: 30
                            spacing: 5
                            Label:
                                text: str(int(interior_intensity.value)) + "%"
                                size_hint_x: None
                                width: 40
                            Slider:
                                id: interior_intensity
                                min: 0
                                max: 100
                                value: root.interior_intensity
                                on_value: root.interior_intensity = self.value; root.update_interior_color()

                # DECK LIGHTNING
                BoxLayout:
                    orientation: "vertical"
                    padding: 10
                    size_hint: 1, 1
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.deck_active else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 100  # Stała wysokość dla sekcji ikony
                        Image:
                            source: "icons/lightning.png"
                            size_hint_y: None
                            height: 64
                            pos_hint: {'center_x': 0.5, 'center_y': 0.5}
                    Label:
                        id: deck_label
                        text: app.get_translation("DECK LIGHTNING") if app else "DECK LIGHTNING"
                        font_size: 18
                        size_hint_y: None
                        height: 30
                    Button:
                        id: deck_btn
                        text: app.get_translation("off") if app else "off"
                        size_hint_y: None
                        height: 40
                        pos_hint: {'center_x': 0.5}
                        on_press: root.on_deck_light()

            # SYSTEM STATUS
            BoxLayout:
                orientation: "vertical"
                padding: 10
                size_hint_y: None
                size_hint_x: 0.25   # <-- węższy kafelek
                pos_hint: {'center_x': 0.5}  # <-- wyśrodkowanie
                height: 100
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [10]
                Label:
                    id: system_status_label
                    text: app.get_translation("SYSTEM STATUS") if app else "SYSTEM STATUS"
                    font_size: 18
                    halign: "center"
                Label:
                    text: "OK"
                    font_size: 24
                    halign: "center"
                    color: 0, 1, 0, 1  # RGBA: zielony kolor (0,255,0)

            # PRZYCISK NA DOLE
            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 50
                Button:
                    id: go_home_btn
                    text: app.get_translation("Go to Home") if app else "Go to Home"
                    size_hint: None, None
                    size: 150, 40
                    pos_hint: {'center_x': 0.5}
                    on_press: root.go_to_home()
                    canvas.before:
                        Color:
                            rgba: 0.3, 0.6, 0.8, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [5]