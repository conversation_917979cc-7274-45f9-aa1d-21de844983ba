#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Remaining Untranslated Texts
Automatically updates all remaining untranslated texts in .kv files
"""

import os
import re

def fix_file_translations(file_path, replacements):
    """Fix translations in a single file"""
    print(f"\n🔧 Fixing translations in {file_path}...")
    
    if not os.path.exists(file_path):
        print(f"⚠️ File not found: {file_path}")
        return 0
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    fixes_applied = 0
    
    for old_pattern, new_pattern in replacements.items():
        if old_pattern in content:
            content = content.replace(old_pattern, new_pattern)
            fixes_applied += 1
            print(f"  ✅ Fixed: {old_pattern}")
    
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"  📝 Applied {fixes_applied} fixes to {file_path}")
    else:
        print(f"  ℹ️ No changes needed in {file_path}")
    
    return fixes_applied

def main():
    """Main function to fix all remaining translations"""
    print("🔧 Fixing All Remaining Untranslated Texts")
    print("=" * 60)
    
    # Define all the fixes needed for each file
    fixes = {
        'ui/settings.kv': {
            # Language buttons
            "text: 'English'": "text: app.get_translation('English') if app else 'English'",
            "text: 'Polski'": "text: app.get_translation('Polski') if app else 'Polski'",
            "text: 'Deutsch'": "text: app.get_translation('Deutsch') if app else 'Deutsch'",
            "text: 'Русский'": "text: app.get_translation('Русский') if app else 'Русский'",
            
            # System section
            "text: 'Firmware Version'": "text: app.get_translation('Firmware Version') if app else 'Firmware Version'",
            "text: 'Update Firmware'": "text: app.get_translation('Update Firmware') if app else 'Update Firmware'",
            "text: 'Factory Reset'": "text: app.get_translation('Factory Reset') if app else 'Factory Reset'",
            
            # Safety section
            "text: 'PIN Code Protection'": "text: app.get_translation('PIN Code Protection') if app else 'PIN Code Protection'",
            "hint_text: 'Enter 4-digit PIN'": "hint_text: app.get_translation('Enter 4-digit PIN') if app else 'Enter 4-digit PIN'",
            "text: 'Set PIN'": "text: app.get_translation('Set PIN') if app else 'Set PIN'",
            "text: 'PIN Protection'": "text: app.get_translation('PIN Protection') if app else 'PIN Protection'",
            "text: 'Emergency Contact/Frequency'": "text: app.get_translation('Emergency Contact/Frequency') if app else 'Emergency Contact/Frequency'",
            "text: 'Secure Mode'": "text: app.get_translation('Secure Mode') if app else 'Secure Mode'",
            
            # Connection section
            "text: 'WiFi'": "text: app.get_translation('WiFi') if app else 'WiFi'",
            "text: 'Scan Networks'": "text: app.get_translation('Scan Networks') if app else 'Scan Networks'",
            "text: 'Connected to:'": "text: app.get_translation('Connected to:') if app else 'Connected to:'",
            "text: 'Available Networks:'": "text: app.get_translation('Available Networks:') if app else 'Available Networks:'",
            "text: 'Password:'": "text: app.get_translation('Password:') if app else 'Password:'",
            "hint_text: 'Enter WiFi password'": "hint_text: app.get_translation('Enter WiFi password') if app else 'Enter WiFi password'",
            "text: 'Connect'": "text: app.get_translation('Connect') if app else 'Connect'",
            "text: 'Bluetooth'": "text: app.get_translation('Bluetooth') if app else 'Bluetooth'",
            "text: 'NMEA2000/CANBus ID'": "text: app.get_translation('NMEA2000/CANBus ID') if app else 'NMEA2000/CANBus ID'",
            
            # Calibration section
            "text: 'Calibrate Compass'": "text: app.get_translation('Calibrate Compass') if app else 'Calibrate Compass'",
            "text: 'Water Sensor Zero Point'": "text: app.get_translation('Water Sensor Zero Point') if app else 'Water Sensor Zero Point'",
            "text: 'Fuel Sensor Zero Point'": "text: app.get_translation('Fuel Sensor Zero Point') if app else 'Fuel Sensor Zero Point'",
            "text: 'Test Actuators'": "text: app.get_translation('Test Actuators') if app else 'Test Actuators'",
        },
        
        'ui/engine.kv': {
            "text: 'START' if app.engine_status != \"active\" else 'STOP'": "text: (app.get_translation('START') if app.engine_status != 'active' else app.get_translation('STOP')) if app else ('START' if app.engine_status != 'active' else 'STOP')",
            "text: 'RPM:'": "text: app.get_translation('RPM:') if app else 'RPM:'",
            "text: 'Temperature:'": "text: app.get_translation('Temperature:') if app else 'Temperature:'",
            "text: 'Oil Pressure:'": "text: app.get_translation('Oil Pressure:') if app else 'Oil Pressure:'",
            "text: 'Engine Hours:'": "text: app.get_translation('Engine Hours:') if app else 'Engine Hours:'",
            "text: 'SYSTEM STATUS'": "text: app.get_translation('SYSTEM STATUS') if app else 'SYSTEM STATUS'",
        },
        
        'ui/alarm.kv': {
            'text: "🔊 BUZZER ACTIVE" if app.get_buzzer_status() else "🔇 Buzzer Silent"': 'text: (app.get_translation("🔊 BUZZER ACTIVE") if app.get_buzzer_status() else app.get_translation("🔇 Buzzer Silent")) if app else ("🔊 BUZZER ACTIVE" if app.get_buzzer_status() else "🔇 Buzzer Silent")',
            'text: "Acknowledge All"': 'text: app.get_translation("Acknowledge All") if app else "Acknowledge All"',
            'text: "Add Test Alarm"': 'text: app.get_translation("Add Test Alarm") if app else "Add Test Alarm"',
            'text: "Go to Home"': 'text: app.get_translation("Go to Home") if app else "Go to Home"',
            'text: "ACTIVE" if root.alarm_active else "INACTIVE"': 'text: (app.get_translation("ACTIVE") if root.alarm_active else app.get_translation("INACTIVE")) if app else ("ACTIVE" if root.alarm_active else "INACTIVE")',
            'text: "Status:"': 'text: app.get_translation("Status:") if app else "Status:"',
            'text: "ACKNOWLEDGED" if root.alarm_acknowledged else "UNACKNOWLEDGED"': 'text: (app.get_translation("ACKNOWLEDGED") if root.alarm_acknowledged else app.get_translation("UNACKNOWLEDGED")) if app else ("ACKNOWLEDGED" if root.alarm_acknowledged else "UNACKNOWLEDGED")',
            'text: "Acknowledge"': 'text: app.get_translation("Acknowledge") if app else "Acknowledge"',
        },
        
        'ui/lightning.kv': {
            'text: "off"': 'text: app.get_translation("off") if app else "off"',
        },
        
        'ui/climate.kv': {
            "text: '-'": "text: app.get_translation('-') if app else '-'",
            "text: '+'": "text: app.get_translation('+') if app else '+'",
        }
    }
    
    total_fixes = 0
    
    for file_path, file_fixes in fixes.items():
        fixes_applied = fix_file_translations(file_path, file_fixes)
        total_fixes += fixes_applied
    
    print(f"\n📊 Summary:")
    print(f"  Total files processed: {len(fixes)}")
    print(f"  Total fixes applied: {total_fixes}")
    
    if total_fixes > 0:
        print("🎉 All remaining translations have been fixed!")
        print("🌍 Now all GUI texts should be translatable!")
    else:
        print("ℹ️ No fixes were needed.")

if __name__ == "__main__":
    main()
