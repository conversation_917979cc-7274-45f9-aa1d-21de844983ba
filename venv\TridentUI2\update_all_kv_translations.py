#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON><PERSON> to automatically update all .kv files with translation calls
"""

import os
import re

def update_kv_file(file_path, translations_map):
    """Update a single .kv file with translations"""
    print(f"\n🔧 Updating {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        updates_count = 0
        
        # Update each translation
        for pattern, replacement in translations_map.items():
            if pattern in content:
                content = content.replace(pattern, replacement)
                updates_count += 1
                print(f"  ✅ Updated: {pattern}")
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  📝 {updates_count} updates applied to {file_path}")
        else:
            print(f"  ℹ️ No updates needed for {file_path}")
            
        return updates_count
        
    except Exception as e:
        print(f"  ❌ Error updating {file_path}: {e}")
        return 0

def main():
    """Main function to update all .kv files"""
    print("🚀 Starting automatic .kv files translation update")
    print("=" * 60)
    
    # Define translation mappings for each file
    kv_files_updates = {
        'ui/autopilot.kv': {
            "text: 'AUTOPILOT'": "id: autopilot_title\n                text: app.get_translation('AUTOPILOT') if app else 'AUTOPILOT'",
            "text: 'AUTOPILOT STATUS'": "id: autopilot_status_title\n                text: app.get_translation('AUTOPILOT STATUS') if app else 'AUTOPILOT STATUS'",
            'text: "ACTIVE" if app.autopilot else "INACTIVE"': 'text: (app.get_translation("ACTIVE") if app.autopilot else app.get_translation("INACTIVE")) if app else ("ACTIVE" if app.autopilot else "INACTIVE")',
            "text: 'AUTOPILOT CONTROL'": "id: autopilot_control_title\n                    text: app.get_translation('AUTOPILOT CONTROL') if app else 'AUTOPILOT CONTROL'",
            "text: 'DEACTIVATE' if app.autopilot else 'ACTIVATE'": "text: (app.get_translation('DEACTIVATE') if app.autopilot else app.get_translation('ACTIVATE')) if app else ('DEACTIVATE' if app.autopilot else 'ACTIVATE')",
            "text: 'HEADING CONTROL'": "id: heading_control_title\n                    text: app.get_translation('HEADING CONTROL') if app else 'HEADING CONTROL'",
            "text: 'NAVIGATION DATA'": "id: navigation_data_title\n                    text: app.get_translation('NAVIGATION DATA') if app else 'NAVIGATION DATA'",
            "text: 'Current Heading:'": "id: current_heading_label\n                        text: app.get_translation('Current Heading:') if app else 'Current Heading:'",
            "text: 'Speed:'": "id: speed_label\n                        text: app.get_translation('Speed:') if app else 'Speed:'",
            "text: 'GPS Position:'": "id: gps_position_label\n                        text: app.get_translation('GPS Position:') if app else 'GPS Position:'"
        },
        
        'ui/battery.kv': {
            "text: 'BATTERY'": "id: battery_title\n            text: app.get_translation('BATTERY') if app else 'BATTERY'",
            "text: 'BATTERY LEVEL'": "id: battery_level_title\n                text: app.get_translation('BATTERY LEVEL') if app else 'BATTERY LEVEL'",
            "text: 'POWER CONSUMPTION'": "id: power_consumption_title\n                    text: app.get_translation('POWER CONSUMPTION') if app else 'POWER CONSUMPTION'",
            "text: 'Current usage'": "id: current_usage_label\n                    text: app.get_translation('Current usage') if app else 'Current usage'",
            "text: 'BATTERY STATUS'": "id: battery_status_title\n                    text: app.get_translation('BATTERY STATUS') if app else 'BATTERY STATUS'",
            "text: 'CHARGING' if app.charging else 'DISCHARGING'": "text: (app.get_translation('CHARGING') if app.charging else app.get_translation('DISCHARGING')) if app else ('CHARGING' if app.charging else 'DISCHARGING')",
            "text: 'POWER SOURCE'": "id: power_source_title\n                text: app.get_translation('POWER SOURCE') if app else 'POWER SOURCE'",
            "text: 'Battery'": "id: battery_btn\n                    text: app.get_translation('Battery') if app else 'Battery'",
            "text: 'Shore Power'": "id: shore_power_btn\n                    text: app.get_translation('Shore Power') if app else 'Shore Power'",
            "text: 'Solar Panels'": "id: solar_panels_btn\n                    text: app.get_translation('Solar Panels') if app else 'Solar Panels'"
        },
        
        'ui/climate.kv': {
            "text: 'CLIMATE'": "id: climate_title\n            text: app.get_translation('CLIMATE') if app else 'CLIMATE'",
            "text: 'target temp'": "id: target_temp_label\n                    text: app.get_translation('target temp') if app else 'target temp'",
            "text: 'current temp'": "id: current_temp_label\n                    text: app.get_translation('current temp') if app else 'current temp'",
            "text: 'external temp'": "id: external_temp_label\n                    text: app.get_translation('external temp') if app else 'external temp'",
            "text: 'SET TEMPERATURE'": "id: set_temperature_title\n                        text: app.get_translation('SET TEMPERATURE') if app else 'SET TEMPERATURE'",
            "text: 'FAN POWER'": "id: fan_power_title\n                        text: app.get_translation('FAN POWER') if app else 'FAN POWER'",
            "text: 'AUTOMATIC AC'": "id: automatic_ac_title\n                        text: app.get_translation('AUTOMATIC AC') if app else 'AUTOMATIC AC'",
            "text: 'on' if root.auto_ac else 'off'": "text: (app.get_translation('on') if root.auto_ac else app.get_translation('off')) if app else ('on' if root.auto_ac else 'off')",
            "text: 'SET FRIDGE TEMPERATURE'": "id: set_fridge_temp_title\n                    text: app.get_translation('SET FRIDGE TEMPERATURE') if app else 'SET FRIDGE TEMPERATURE'",
            "text: 'SYSTEM STATUS'": "id: climate_system_status_title\n                    text: app.get_translation('SYSTEM STATUS') if app else 'SYSTEM STATUS'"
        },
        
        'ui/fuel.kv': {
            "text: 'FUEL'": "id: fuel_title\n            text: app.get_translation('FUEL') if app else 'FUEL'",
            "text: 'FUEL LEVEL'": "id: fuel_level_title\n                text: app.get_translation('FUEL LEVEL') if app else 'FUEL LEVEL'",
            "text: 'FUEL TANK'": "id: fuel_tank_title\n                    text: app.get_translation('FUEL TANK') if app else 'FUEL TANK'",
            "text: 'FUEL CONSUMPTION'": "id: fuel_consumption_title\n                    text: app.get_translation('FUEL CONSUMPTION') if app else 'FUEL CONSUMPTION'",
            "text: 'Current consumption'": "id: fuel_current_consumption_label\n                    text: app.get_translation('Current consumption') if app else 'Current consumption'",
            "text: 'RANGE ESTIMATION'": "id: range_estimation_title\n                    text: app.get_translation('RANGE ESTIMATION') if app else 'RANGE ESTIMATION'",
            "text: 'Estimated Range:'": "id: estimated_range_label\n                        text: app.get_translation('Estimated Range:') if app else 'Estimated Range:'",
            "text: 'Estimated Distance:'": "id: estimated_distance_label\n                        text: app.get_translation('Estimated Distance:') if app else 'Estimated Distance:'"
        },
        
        'ui/water.kv': {
            "text: 'WATER'": "id: water_title\n            text: app.get_translation('WATER') if app else 'WATER'",
            "text: 'WATER LEVEL'": "id: water_level_title\n                text: app.get_translation('WATER LEVEL') if app else 'WATER LEVEL'",
            "text: 'FRESH WATER TANK'": "id: fresh_water_tank_title\n                    text: app.get_translation('FRESH WATER TANK') if app else 'FRESH WATER TANK'",
            "text: 'WATER PUMP'": "id: water_pump_title\n                    text: app.get_translation('WATER PUMP') if app else 'WATER PUMP'",
            "text: 'ON'": "id: water_pump_btn\n                    text: app.get_translation('ON') if app else 'ON'",
            "text: 'WATER USAGE STATISTICS'": "id: water_usage_stats_title\n                    text: app.get_translation('WATER USAGE STATISTICS') if app else 'WATER USAGE STATISTICS'",
            "text: 'Daily Usage:'": "id: daily_usage_label\n                        text: app.get_translation('Daily Usage:') if app else 'Daily Usage:'",
            "text: 'Estimated Days Left:'": "id: estimated_days_label\n                        text: app.get_translation('Estimated Days Left:') if app else 'Estimated Days Left:'"
        }
    }
    
    total_updates = 0
    
    # Update each file
    for file_path, translations in kv_files_updates.items():
        if os.path.exists(file_path):
            updates = update_kv_file(file_path, translations)
            total_updates += updates
        else:
            print(f"⚠️ File not found: {file_path}")
    
    print(f"\n📊 Summary:")
    print(f"  Total files processed: {len(kv_files_updates)}")
    print(f"  Total updates applied: {total_updates}")
    
    if total_updates > 0:
        print("🎉 All .kv files have been updated with translation calls!")
    else:
        print("ℹ️ No updates were needed.")

if __name__ == "__main__":
    main()
