#:kivy 2.0.0

<Button>:
    background_normal: ''
    background_color: 0, 0, 0, 0

<ClimateScreen>:
    FloatLayout:
        canvas.before:
            Color:
                rgba: 0.05, 0.1, 0.15, 1  # Dark blue background
            Rectangle:
                pos: self.pos
                size: self.size

        # Header (tylko back i ikony)
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 0, 0]
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.manager.current = 'home'
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            Widget:
                size_hint_x: 0.8  # puste miejsce na środku
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # CLIMATE centralnie nad zegarem
        Label:
            id: climate_title
            text: app.get_translation('CLIMATE') if app else 'CLIMATE'
            font_size: '24sp'
            size_hint: None, None
            size: self.texture_size
            pos_hint: {'center_x': 0.5, 'top': 0.92}

        # Time
        ClockWidget:
            id: clock
            font_size: '30sp'
            pos_hint: {'center_x': 0.5, 'top': 0.86}
            size_hint: None, None

        # Temperature Display
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 0.8, 0.2
            pos_hint: {'center_x': 0.5, 'top': 0.8}

            BoxLayout:
                orientation: 'vertical'
                Label:
                    id: target_temp_label
                    text: app.get_translation('target temp') if app else 'target temp'
                    font_size: '18sp'
                Label:
                    text: f"{root.set_temp:.1f}°C"
                    font_size: '48sp'

            BoxLayout:
                orientation: 'vertical'
                Label:
                    id: current_temp_label
                    text: app.get_translation('current temp') if app else 'current temp'
                    font_size: '18sp'
                Label:
                    text: f"{root.current_internal_temp:.1f}°C"
                    font_size: '48sp'

            BoxLayout:
                orientation: 'vertical'
                Label:
                    id: external_temp_label
                    text: app.get_translation('external temp') if app else 'external temp'
                    font_size: '18sp'
                Label:
                    text: f"{app.external_temp:.1f}°C"
                    font_size: '48sp'

        # Control Panels
        BoxLayout:
            size_hint: 0.95, 0.18
            pos_hint: {'center_x': 0.5, 'center_y': 0.5}
            spacing: 30  # odstęp między kafelkami
            padding: 0, 0, 0, 0

            # SET TEMPERATURE
            BoxLayout:
                size_hint_x: 0.27
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                BoxLayout:
                    orientation: 'vertical'
                    padding: '10dp'
                    Image:
                        source: 'icons/ac.png'
                        size_hint_y: 0.4
                    Label:
                        id: set_temperature_title
                        text: app.get_translation('SET TEMPERATURE') if app else 'SET TEMPERATURE'
                        font_size: '14sp'
                    BoxLayout:
                        orientation: 'horizontal'
                        spacing: 10
                        Button:
                            text: app.get_translation('-') if app else '-'
                            on_press: root.decrease_temp()
                        Label:
                            text: f"{root.set_temp:.1f}°C"
                            font_size: '22sp'
                        Button:
                            text: app.get_translation('+') if app else '+'
                            on_press: root.increase_temp()

            # FAN POWER
            BoxLayout:
                size_hint_x: 0.27
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                BoxLayout:
                    orientation: 'vertical'
                    padding: '10dp'
                    Image:
                        source: 'icons/fan.png'
                        size_hint_y: 0.4
                    Label:
                        id: fan_power_title
                        text: app.get_translation('FAN POWER') if app else 'FAN POWER'
                        font_size: '14sp'
                    Slider:
                        id: fan_power_slider
                        min: 0
                        max: 100
                        value: root.fan_power
                        disabled: root.auto_ac  # blokada gdy auto_ac włączone
                        on_value: root.set_fan_power(self.value)

            # AUTOMATIC AC
            BoxLayout:
                size_hint_x: 0.27
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                BoxLayout:
                    orientation: 'vertical'
                    padding: '10dp'
                    Image:
                        source: 'icons/auto.png'
                        size_hint_y: 0.4
                    Label:
                        id: automatic_ac_title
                        text: app.get_translation('AUTOMATIC AC') if app else 'AUTOMATIC AC'
                        font_size: '14sp'
                    Button:
                        text: (app.get_translation('on') if root.auto_ac else app.get_translation('off')) if app else ('on' if root.auto_ac else 'off')
                        background_color: (0, 0.7, 0, 1) if root.auto_ac else (0.3, 0.3, 0.3, 1)
                        on_press: root.toggle_auto_ac()
                        size_hint_y: 0.3

        # Kafelek SET FRIDGE TEMPERATURE wyśrodkowany poniżej:
        BoxLayout:
            size_hint: 0.35, 0.15
            pos_hint: {'center_x': 0.5, 'center_y': 0.32}
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]
            BoxLayout:
                orientation: 'vertical'
                padding: '10dp'
                Image:
                    source: 'icons/fridge.png'
                    size_hint_y: 0.4
                Label:
                    id: set_fridge_temp_title
                    text: app.get_translation('SET FRIDGE TEMPERATURE') if app else 'SET FRIDGE TEMPERATURE'
                    font_size: '14sp'
                BoxLayout:
                    orientation: 'horizontal'
                    spacing: 10
                    Button:
                        text: app.get_translation('-') if app else '-'
                        on_press: root.decrease_fridge_temp()
                    Label:
                        text: f"{root.fridge_temp:.1f}°C"
                        font_size: '22sp'
                    Button:
                        text: app.get_translation('+') if app else '+'
                        on_press: root.increase_fridge_temp()

        # System Status
        BoxLayout:
            size_hint: 0.3, 0.1
            pos_hint: {'center_x': 0.5, 'center_y': 0.15}
            canvas.before:
                Color:
                    rgba: 0.1, 0.2, 0.3, 1
                RoundedRectangle:
                    pos: self.pos
                    size: self.size
                    radius: [15]

            BoxLayout:
                orientation: 'vertical'
                Label:
                    id: climate_system_status_title
                    text: app.get_translation('SYSTEM STATUS') if app else 'SYSTEM STATUS'
                    font_size: '14sp'
                Label:
                    id: system_status
                    text: 'OK' if root.ac_mode == 'off' else ('HEATING' if root.ac_mode == 'heating' else 'COOLING')
                    color: (0, 1, 0, 1) if root.ac_mode == 'off' else ((1, 0.5, 0, 1) if root.ac_mode == 'heating' else (0, 0.7, 1, 1))