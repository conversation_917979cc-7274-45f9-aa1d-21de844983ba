#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test for Complete Home Screen Translation
Tests if all elements on the home screen are properly translated
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_status_translations():
    """Test status value translations"""
    print("🧪 Testing Status Translations")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Test status translations
        status_keys = [
            "on", "off", "active", "inactive", "OK", "ALARM", 
            "WARNING", "CRITICAL", "HIGH TEMP", "HIGH RPM", "NORMAL"
        ]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        for language in languages:
            print(f"\n🌍 Testing {language}:")
            set_global_language(language)
            
            for key in status_keys[:6]:  # Test first 6 keys
                translation = tr(key)
                print(f"  {key}: {translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Status translations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_home_kv_updates():
    """Test if home.kv has been updated correctly"""
    print("\n🧪 Testing Home KV Updates")
    print("=" * 50)
    
    try:
        # Check home.kv
        print("\n📄 Checking home.kv...")
        with open('ui/home.kv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for app.get_translation usage
        translation_calls = content.count('app.get_translation')
        print(f"  Found {translation_calls} app.get_translation calls")
        
        # Check for specific status translations
        status_translations = [
            'app.get_translation("on")',
            'app.get_translation(app.alarm_status)',
            'app.get_translation(app.engine_status)',
            'app.get_translation("off")'
        ]
        
        found_status = 0
        for status_trans in status_translations:
            if status_trans in content:
                found_status += 1
                print(f"  ✅ Found: {status_trans}")
            else:
                print(f"  ❌ Missing: {status_trans}")
        
        print(f"\n📊 Found {found_status}/{len(status_translations)} status translations")
        print(f"📊 Found {translation_calls} total translation calls")
        
        return translation_calls > 0 and found_status > 0
        
    except Exception as e:
        print(f"❌ Home KV updates test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_status_translations():
    """Test specific status translations"""
    print("\n🧪 Testing Specific Status Translations")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language
        from translatable_screen import tr
        
        # Test specific status translations
        test_cases = [
            ("Polski", "active", "aktywny"),
            ("Polski", "inactive", "nieaktywny"),
            ("Polski", "on", "wł"),
            ("Polski", "off", "wył"),
            ("Deutsch", "active", "aktiv"),
            ("Deutsch", "inactive", "inaktiv"),
            ("Deutsch", "on", "ein"),
            ("Deutsch", "off", "aus"),
            ("Русский", "active", "активный"),
            ("Русский", "inactive", "неактивный"),
            ("Русский", "on", "вкл"),
            ("Русский", "off", "выкл")
        ]
        
        for language, key, expected in test_cases:
            set_global_language(language)
            translation = tr(key)
            status = "✅" if translation == expected else "❌"
            print(f"  {status} {language} - {key}: {translation} (expected: {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ Specific status translations test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_home_screen_mappings():
    """Test home screen translation mappings"""
    print("\n🧪 Testing Home Screen Mappings")
    print("=" * 50)
    
    try:
        import main
        
        # Create app instance
        app = main.MainApp()
        
        # Check if home mappings exist and include status elements
        if hasattr(app, 'update_screen_translations'):
            print("✅ update_screen_translations method found")
            
            # Create mock screen to test mappings
            class MockScreen:
                def __init__(self):
                    self.ids = {}
                    # Add mock widgets for home screen
                    home_ids = [
                        'home_title_label', 'lightning_label', 'engine_label',
                        'lightning_status_btn', 'engine_status', 'autopilot_status'
                    ]
                    for widget_id in home_ids:
                        mock_widget = MockWidget()
                        self.ids[widget_id] = mock_widget
            
            class MockWidget:
                def __init__(self):
                    self.text = "Original Text"
            
            # Test home mappings
            mock_screen = MockScreen()
            
            print("  Testing home screen mappings...")
            app.update_screen_translations(mock_screen, 'home')
            
            # Check if widgets were updated
            for widget_id, widget in mock_screen.ids.items():
                print(f"    {widget_id}: {widget.text}")
            
            return True
        else:
            print("❌ update_screen_translations method not found")
            return False
        
    except Exception as e:
        print(f"❌ Home screen mappings test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dynamic_status_handling():
    """Test dynamic status handling in app"""
    print("\n🧪 Testing Dynamic Status Handling")
    print("=" * 50)
    
    try:
        import main
        
        # Create app instance
        app = main.MainApp()
        
        # Test if app has status properties
        status_properties = ['engine_status', 'alarm_status', 'autopilot']
        
        for prop in status_properties:
            if hasattr(app, prop):
                value = getattr(app, prop)
                print(f"  ✅ {prop}: {value}")
            else:
                print(f"  ❌ Missing property: {prop}")
        
        # Test translation of current status values
        print("\n  Testing status value translations...")
        
        if hasattr(app, 'engine_status'):
            engine_translation = app.get_translation(app.engine_status)
            print(f"    engine_status ({app.engine_status}): {engine_translation}")
        
        if hasattr(app, 'alarm_status'):
            alarm_translation = app.get_translation(app.alarm_status)
            print(f"    alarm_status ({app.alarm_status}): {alarm_translation}")
        
        if hasattr(app, 'autopilot'):
            autopilot_status = "on" if app.autopilot else "off"
            autopilot_translation = app.get_translation(autopilot_status)
            print(f"    autopilot ({autopilot_status}): {autopilot_translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Dynamic status handling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Starting Complete Home Screen Translation Tests")
    print("=" * 60)
    
    tests = [
        test_status_translations,
        test_home_kv_updates,
        test_specific_status_translations,
        test_home_screen_mappings,
        test_dynamic_status_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        
        print("-" * 60)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Complete home screen translations should work!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
