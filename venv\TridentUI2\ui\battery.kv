#:kivy 2.0.0

<BatteryScreen>:
    FloatLayout:
        canvas.before:
            Color:
                rgba: 0.05, 0.1, 0.15, 1  # Ciemne niebieskie tło
            Rectangle:
                pos: self.pos
                size: self.size

        # Header z przyciskiem powrotu i ikonami
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 0, 0]
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Label:
                    text: "<"
                    font_size: '24sp'
                    color: 0.9, 0.95, 1, 1
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
            Widget:
                size_hint_x: 0.8  # puste miejsce na środku
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'images/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'images/settings.png'
                    size_hint: 0.5, 0.5

        # Tytuł BATTERY
        Label:
            id: battery_title
            text: app.get_translation('BATTERY') if app else 'BATTERY'
            font_size: '24sp'
            size_hint: None, None
            size: self.texture_size
            pos_hint: {'center_x': 0.5, 'top': 0.92}

        # Zegar
        ClockWidget:
            id: clock
            font_size: '30sp'
            pos_hint: {'center_x': 0.5, 'top': 0.86}
            size_hint: None, None

        # Główny wskaźnik poziomu baterii
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.8, 0.2  # Zmniejszona wysokość
            pos_hint: {'center_x': 0.5, 'top': 0.8}
            spacing: 10

            # Poziom naładowania baterii
            Label:
                id: battery_level_title
                text: app.get_translation('BATTERY LEVEL') if app else 'BATTERY LEVEL'
                font_size: '20sp'
                size_hint_y: None
                height: 30

            BoxLayout:
                orientation: 'horizontal'
                size_hint_y: None
                height: 60  # Zmniejszona wysokość
                spacing: 20

                # Procent naładowania
                Label:
                    id: battery_level_label
                    text: f"{int(app.battery_level)}%"
                    font_size: '48sp'  # Zmniejszona czcionka
                    size_hint_x: 0.3

                # Pasek postępu
                BoxLayout:
                    orientation: 'vertical'
                    size_hint_x: 0.7
                    padding: [0, 10, 0, 10]  # Zmniejszony padding

                    ProgressBar:
                        value: app.battery_level
                        max: 100
                        size_hint_y: None
                        height: 40
                        background_color: 0.1, 0.1, 0.1, 1
                        color: 0.2, 0.7, 0.3, 1 if app.battery_level > 20 else 0.8, 0.2, 0.2, 1

        # Informacje o zużyciu energii i statusie
        GridLayout:
            cols: 2
            size_hint: 0.9, 0.35  # Zmniejszona wysokość
            pos_hint: {'center_x': 0.5, 'center_y': 0.4}  # Przesunięta w dół
            spacing: 15  # Zmniejszony odstęp
            padding: 10

            # Zużycie energii
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: power_consumption_title
                    text: app.get_translation('POWER CONSUMPTION') if app else 'POWER CONSUMPTION'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'images/battery.png'
                    size_hint_y: None
                    height: 48  # Zmniejszona wysokość
                    pos_hint: {'center_x': 0.5}

                Label:
                    text: f"{app.power_consumption} kW"
                    font_size: '28sp'  # Zmniejszona czcionka
                    color: 0.9, 0.95, 1, 1

                Label:
                    id: current_usage_label
                    text: app.get_translation('Current usage') if app else 'Current usage'
                    font_size: '14sp'
                    color: 0.7, 0.75, 0.8, 1

            # Status ładowania
            BoxLayout:
                orientation: 'vertical'
                canvas.before:
                    Color:
                        rgba: 0.1, 0.2, 0.3, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                padding: 15
                spacing: 10

                Label:
                    id: battery_status_title
                    text: app.get_translation('BATTERY STATUS') if app else 'BATTERY STATUS'
                    font_size: '18sp'
                    size_hint_y: None
                    height: 30

                Image:
                    source: 'images/battery.png'
                    size_hint_y: None
                    height: 48  # Zmniejszona wysokość
                    pos_hint: {'center_x': 0.5}

                BoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: 50
                    spacing: 10

                    # Ikona statusu
                    Image:
                        source: 'images/battery.png'
                        size_hint_x: None
                        width: 40

                    # Wskaźnik statusu
                    Label:
                        id: charging_status
                        text: (app.get_translation('CHARGING') if app.charging else app.get_translation('DISCHARGING')) if app else ('CHARGING' if app.charging else 'DISCHARGING')
                        font_size: '22sp'
                        color: (0, 0.8, 0, 1) if app.charging else (0.8, 0.3, 0.3, 1)

                Label:
                    text: app.time_remaining
                    font_size: '16sp'
                    color: (0, 0.8, 0, 1) if app.charging else (0.8, 0.3, 0.3, 1)

        # Źródła zasilania
        BoxLayout:
            orientation: 'vertical'
            size_hint: 0.9, 0.15  # Zmniejszona wysokość
            pos_hint: {'center_x': 0.5, 'center_y': 0.13}  # Przesunięta w dół

            Label:
                id: power_source_title
                text: app.get_translation('POWER SOURCE') if app else 'POWER SOURCE'
                font_size: '18sp'
                size_hint_y: None
                height: 30

            BoxLayout:
                orientation: 'horizontal'
                spacing: 15  # Zmniejszony odstęp
                size_hint_y: None
                height: 40  # Określona wysokość

                # Bateria
                ToggleButton:
                    id: battery_btn
                    text: app.get_translation('Battery') if app else 'Battery'
                    group: 'power_source'
                    state: 'down' if app.power_source == 'Battery' else 'normal'
                    on_press: root.change_power_source('Battery')
                    background_color: (0.2, 0.4, 0.6, 1) if self.state == 'down' else (0.1, 0.2, 0.3, 1)

                # Zasilanie z lądu
                ToggleButton:
                    id: shore_power_btn
                    text: app.get_translation('Shore Power') if app else 'Shore Power'
                    group: 'power_source'
                    state: 'down' if app.power_source == 'Shore Power' else 'normal'
                    on_press: root.change_power_source('Shore Power')
                    background_color: (0.2, 0.4, 0.6, 1) if self.state == 'down' else (0.1, 0.2, 0.3, 1)

                # Solar Panels
                ToggleButton:
                    id: solar_panels_btn
                    text: app.get_translation('Solar Panels') if app else 'Solar Panels'
                    group: 'power_source'
                    state: 'down' if app.power_source == 'Solar Panels' else 'normal'
                    on_press: root.change_power_source('Solar Panels')
                    background_color: (0.2, 0.4, 0.6, 1) if self.state == 'down' else (0.1, 0.2, 0.3, 1)
