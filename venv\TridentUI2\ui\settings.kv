#:kivy 2.0.0

<SettingsScreen>:
    canvas.before:
        Color:
            rgba: 0.05, 0.1, 0.15, 1  # Dark navy background
        Rectangle:
            pos: self.pos
            size: self.size

    FloatLayout:
        # Header with back button, title, and clock
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.1
            pos_hint: {'top': 1}
            padding: [12, 12, 12, 0]
            
            # Back button
            Button:
                size_hint: None, None
                size: 40, 40
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_home()
                Image:
                    source: 'icons/back.png'
                    size_hint: None, None
                    size: 28, 28
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True
            
            # Title and clock in center
            BoxLayout:
                orientation: 'vertical'
                size_hint_x: 0.8
                
                Label:
                    text: app.get_translation('SETTINGS') if app else 'SETTINGS'
                    font_size: '24sp'
                    size_hint_y: 0.6
                    halign: 'center'
                    valign: 'bottom'
                
                ClockWidget:
                    id: clock
                    font_size: '20sp'
                    size_hint_y: 0.4
                    halign: 'center'
                    valign: 'top'
            
            # WiFi and Settings icons
            BoxLayout:
                size_hint_x: 0.1
                spacing: '10dp'
                Image:
                    source: 'icons/wifi.png'
                    size_hint: 0.5, 0.5
                Image:
                    source: 'icons/settings.png'
                    size_hint: 0.5, 0.5

        # Main content area
        BoxLayout:
            orientation: 'horizontal'
            size_hint: 1, 0.9
            pos_hint: {'top': 0.9}
            spacing: 20
            padding: [20, 0, 20, 20]
            
            # Left sidebar with category buttons
            BoxLayout:
                orientation: 'vertical'
                size_hint_x: 0.25
                spacing: 10
                
                # DISPLAY button
                Button:
                    id: display_btn
                    text: app.get_translation('DISPLAY') if app else 'DISPLAY'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'DISPLAY' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('DISPLAY')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'DISPLAY' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # LANGUAGE button
                Button:
                    id: language_btn
                    text: app.get_translation('LANGUAGE') if app else 'LANGUAGE'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'LANGUAGE' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('LANGUAGE')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'LANGUAGE' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # SYSTEM button
                Button:
                    id: system_btn
                    text: app.get_translation('SYSTEM') if app else 'SYSTEM'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SYSTEM' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('SYSTEM')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SYSTEM' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # SAFETY button
                Button:
                    id: safety_btn
                    text: app.get_translation('SAFETY') if app else 'SAFETY'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SAFETY' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('SAFETY')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'SAFETY' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # CONNECTION button
                Button:
                    id: connection_btn
                    text: app.get_translation('CONNECTION') if app else 'CONNECTION'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CONNECTION' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('CONNECTION')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CONNECTION' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # CALIBRATION button
                Button:
                    id: calibration_btn
                    text: app.get_translation('CALIBRATION') if app else 'CALIBRATION'
                    font_size: '16sp'
                    size_hint_y: None
                    height: 60
                    background_normal: ''
                    background_color: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CALIBRATION' else (0.1, 0.2, 0.3, 1)
                    on_press: root.select_category('CALIBRATION')
                    canvas.before:
                        Color:
                            rgba: (0.2, 0.4, 0.6, 1) if root.selected_category == 'CALIBRATION' else (0.1, 0.2, 0.3, 1)
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [10]
                
                # Spacer
                Widget:
            
            # Right panel with settings content
            BoxLayout:
                orientation: 'vertical'
                size_hint_x: 0.75
                canvas.before:
                    Color:
                        rgba: 0.08, 0.15, 0.22, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [15]
                
                # Content area with scrollable view
                ScrollView:
                    do_scroll_x: False
                    do_scroll_y: True
                    
                    BoxLayout:
                        id: settings_content
                        orientation: 'vertical'
                        size_hint_y: None
                        height: self.minimum_height
                        padding: [20, 20]
                        spacing: 15
                        
                        # DISPLAY SETTINGS (visible when DISPLAY is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'DISPLAY' else 0
                            opacity: 1 if root.selected_category == 'DISPLAY' else 0
                            spacing: 15
                            
                            # Brightness setting
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 80
                                
                                Label:
                                    id: brightness_label
                                    text: app.get_translation('Brightness') if app else 'Brightness'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'
                                
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10
                                    
                                    Slider:
                                        id: brightness_slider
                                        min: 10
                                        max: 100
                                        value: root.brightness
                                        on_value: root.set_brightness(self.value)
                                        size_hint_x: 0.8
                                    
                                    Label:
                                        text: f"{int(root.brightness)}%"
                                        font_size: '16sp'
                                        size_hint_x: 0.2
                                        halign: 'center'
                            
                            # Theme setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20
                                
                                Label:
                                    id: theme_mode_label
                                    text: app.get_translation('Theme Mode') if app else 'Theme Mode'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'
                                
                                Button:
                                    text: root.theme_mode
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.theme_mode == 'Night' else (0.8, 0.6, 0, 1)
                                    on_press: root.toggle_theme()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.theme_mode == 'Night' else (0.8, 0.6, 0, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]
                            
                            # Screen orientation setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20
                                
                                Label:
                                    id: screen_orientation_label
                                    text: app.get_translation('Screen Orientation') if app else 'Screen Orientation'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'
                                
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_x: 0.5
                                    spacing: 10
                                    
                                    Button:
                                        text: app.get_translation('Landscape') if app else 'Landscape'
                                        font_size: '14sp'
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Landscape' else (0.3, 0.3, 0.3, 1)
                                        on_press: root.set_orientation('Landscape')
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Landscape' else (0.3, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]
                                    
                                    Button:
                                        text: app.get_translation('Portrait') if app else 'Portrait'
                                        font_size: '14sp'
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Portrait' else (0.3, 0.3, 0.3, 1)
                                        on_press: root.set_orientation('Portrait')
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1) if root.screen_orientation == 'Portrait' else (0.3, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                        # LANGUAGE SETTINGS (visible when LANGUAGE is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'LANGUAGE' else 0
                            opacity: 1 if root.selected_category == 'LANGUAGE' else 0
                            spacing: 15

                            # Language selection
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 320
                                spacing: 15

                                Label:
                                    id: language_title_label
                                    text: app.get_translation('Language / Język / Sprache / Язык') if app else 'Language / Język / Sprache / Язык'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: app.get_translation('English') if app else 'English'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 60
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1) if root.language == 'English' else (0.3, 0.3, 0.3, 1)
                                    on_press: root.set_language_english()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1) if root.language == 'English' else (0.3, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                                Button:
                                    text: app.get_translation('Polski') if app else 'Polski'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 60
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1) if root.language == 'Polski' else (0.3, 0.3, 0.3, 1)
                                    on_press: root.set_language_polish()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1) if root.language == 'Polski' else (0.3, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                                Button:
                                    text: app.get_translation('Deutsch') if app else 'Deutsch'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 60
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1) if root.language == 'Deutsch' else (0.3, 0.3, 0.3, 1)
                                    on_press: root.set_language_german()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1) if root.language == 'Deutsch' else (0.3, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                                Button:
                                    text: app.get_translation('Русский') if app else 'Русский'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 60
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1) if root.language == 'Русский' else (0.3, 0.3, 0.3, 1)
                                    on_press: root.set_language_russian()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1) if root.language == 'Русский' else (0.3, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # Time format setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    id: time_format_label
                                    text: app.get_translation('Time Format') if app else 'Time Format'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: root.time_format
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1)
                                    on_press: root.toggle_time_format()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # Units setting
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    id: units_label
                                    text: app.get_translation('Units') if app else 'Units'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: root.units
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0.2, 0.4, 0.6, 1)
                                    on_press: root.toggle_units()
                                    canvas.before:
                                        Color:
                                            rgba: (0.2, 0.4, 0.6, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                        # SYSTEM SETTINGS (visible when SYSTEM is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'SYSTEM' else 0
                            opacity: 1 if root.selected_category == 'SYSTEM' else 0
                            spacing: 15

                            # Firmware version (readonly)
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: app.get_translation('Firmware Version') if app else 'Firmware Version'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Label:
                                    text: root.firmware_version
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    halign: 'right'
                                    valign: 'middle'
                                    color: 0.7, 0.7, 0.7, 1

                            # Update firmware button
                            Button:
                                text: app.get_translation('Update Firmware') if app else 'Update Firmware'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.8, 0.6, 0, 1)
                                on_press: root.update_firmware()
                                canvas.before:
                                    Color:
                                        rgba: (0.8, 0.6, 0, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                            # Restart system button
                            Button:
                                id: restart_system_btn
                                text: app.get_translation('Restart System') if app else 'Restart System'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.6, 0.4, 0, 1)
                                on_press: root.restart_system()
                                canvas.before:
                                    Color:
                                        rgba: (0.6, 0.4, 0, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                            # Factory reset button
                            Button:
                                text: app.get_translation('Factory Reset') if app else 'Factory Reset'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.8, 0.2, 0.2, 1)
                                on_press: root.factory_reset()
                                canvas.before:
                                    Color:
                                        rgba: (0.8, 0.2, 0.2, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                        # SAFETY SETTINGS (visible when SAFETY is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'SAFETY' else 0
                            opacity: 1 if root.selected_category == 'SAFETY' else 0
                            spacing: 15

                            # PIN Code Setup
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 120
                                spacing: 10

                                Label:
                                    text: app.get_translation('PIN Code Protection') if app else 'PIN Code Protection'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 40
                                    spacing: 10

                                    TextInput:
                                        id: pin_input
                                        hint_text: app.get_translation('Enter 4-digit PIN') if app else 'Enter 4-digit PIN'
                                        font_size: '16sp'
                                        size_hint_x: 0.6
                                        multiline: False
                                        password: True
                                        input_filter: 'int'
                                        background_color: 0.1, 0.2, 0.3, 1
                                        foreground_color: 1, 1, 1, 1
                                        on_focus: root.show_virtual_keyboard(self, True, True) if self.focus else root.hide_virtual_keyboard()

                                    Button:
                                        text: app.get_translation('Set PIN') if app else 'Set PIN'
                                        font_size: '14sp'
                                        size_hint_x: 0.4
                                        background_normal: ''
                                        background_color: (0, 0.7, 0, 1)
                                        on_press: root.set_pin_code(pin_input.text)
                                        canvas.before:
                                            Color:
                                                rgba: (0, 0.7, 0, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 40
                                    spacing: 20

                                    Label:
                                        text: app.get_translation('PIN Protection') if app else 'PIN Protection'
                                        font_size: '16sp'
                                        size_hint_x: 0.5
                                        halign: 'left'
                                        valign: 'middle'

                                    Button:
                                        text: 'ON' if root.pin_enabled else 'OFF'
                                        font_size: '16sp'
                                        size_hint_x: 0.5
                                        background_normal: ''
                                        background_color: (0, 0.7, 0, 1) if root.pin_enabled else (0.8, 0.3, 0.3, 1)
                                        on_press: root.toggle_settings_lock()
                                        canvas.before:
                                            Color:
                                                rgba: (0, 0.7, 0, 1) if root.pin_enabled else (0.8, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                            # Emergency contact setting
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 100
                                spacing: 10

                                Label:
                                    text: app.get_translation('Emergency Contact/Frequency') if app else 'Emergency Contact/Frequency'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                TextInput:
                                    text: root.emergency_contact
                                    font_size: '16sp'
                                    size_hint_y: None
                                    height: 40
                                    multiline: False
                                    on_text: root.set_emergency_contact(self.text)
                                    background_color: 0.1, 0.2, 0.3, 1
                                    foreground_color: 1, 1, 1, 1

                            # Secure mode toggle
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: app.get_translation('Secure Mode') if app else 'Secure Mode'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: 'ON' if root.secure_mode else 'OFF'
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.secure_mode else (0.8, 0.3, 0.3, 1)
                                    on_press: root.toggle_secure_mode()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.secure_mode else (0.8, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                        # CONNECTION SETTINGS (visible when CONNECTION is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'CONNECTION' else 0
                            opacity: 1 if root.selected_category == 'CONNECTION' else 0
                            spacing: 15

                            # WiFi Management Section
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 300
                                spacing: 15

                                # WiFi toggle and scan
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10

                                    Label:
                                        text: app.get_translation('WiFi') if app else 'WiFi'
                                        font_size: '18sp'
                                        size_hint_x: 0.3
                                        halign: 'left'
                                        valign: 'middle'

                                    Button:
                                        text: 'ON' if root.wifi_enabled else 'OFF'
                                        font_size: '16sp'
                                        size_hint_x: 0.3
                                        background_normal: ''
                                        background_color: (0, 0.7, 0, 1) if root.wifi_enabled else (0.8, 0.3, 0.3, 1)
                                        on_press: root.toggle_wifi()
                                        canvas.before:
                                            Color:
                                                rgba: (0, 0.7, 0, 1) if root.wifi_enabled else (0.8, 0.3, 0.3, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                                    Button:
                                        text: app.get_translation('Scan Networks') if app else 'Scan Networks'
                                        font_size: '14sp'
                                        size_hint_x: 0.4
                                        background_normal: ''
                                        background_color: (0.2, 0.4, 0.6, 1)
                                        on_press: root.scan_wifi_networks()
                                        canvas.before:
                                            Color:
                                                rgba: (0.2, 0.4, 0.6, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                                # Current connection status
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 40
                                    spacing: 10

                                    Label:
                                        text: app.get_translation('Connected to:') if app else 'Connected to:'
                                        font_size: '16sp'
                                        size_hint_x: 0.4
                                        halign: 'left'
                                        valign: 'middle'

                                    Label:
                                        text: root.connected_network if root.connected_network else 'Not connected'
                                        font_size: '16sp'
                                        size_hint_x: 0.6
                                        halign: 'left'
                                        valign: 'middle'
                                        color: (0, 1, 0, 1) if root.connected_network else (0.8, 0.3, 0.3, 1)

                                # Available networks list
                                Label:
                                    text: app.get_translation('Available Networks:') if app else 'Available Networks:'
                                    font_size: '16sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                ScrollView:
                                    size_hint_y: None
                                    height: 120
                                    do_scroll_x: False

                                    GridLayout:
                                        id: networks_list
                                        cols: 1
                                        size_hint_y: None
                                        height: self.minimum_height
                                        spacing: 5

                                        # Dynamic network buttons will be added here
                                        # For now, show placeholder if no networks
                                        Label:
                                            text: (app.get_translation('No networks found. Press "Scan Networks" to search.') if not root.available_networks else '') if app else ('No networks found. Press "Scan Networks" to search.' if not root.available_networks else '')
                                            font_size: '14sp'
                                            size_hint_y: None
                                            height: 40 if not root.available_networks else 0
                                            opacity: 1 if not root.available_networks else 0
                                            halign: 'center'
                                            valign: 'middle'
                                            color: 0.7, 0.7, 0.7, 1

                                # WiFi password input (shown when needed)
                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 40 if root.wifi_password else 0
                                    opacity: 1 if root.wifi_password else 0
                                    spacing: 10

                                    Label:
                                        text: app.get_translation('Password:') if app else 'Password:'
                                        font_size: '16sp'
                                        size_hint_x: 0.3
                                        halign: 'left'
                                        valign: 'middle'

                                    TextInput:
                                        id: wifi_password_input
                                        hint_text: app.get_translation('Enter WiFi password') if app else 'Enter WiFi password'
                                        font_size: '16sp'
                                        size_hint_x: 0.5
                                        multiline: False
                                        password: True
                                        background_color: 0.1, 0.2, 0.3, 1
                                        foreground_color: 1, 1, 1, 1
                                        on_focus: root.show_virtual_keyboard(self, True, False) if self.focus else root.hide_virtual_keyboard()

                                    Button:
                                        text: app.get_translation('Connect') if app else 'Connect'
                                        font_size: '14sp'
                                        size_hint_x: 0.2
                                        background_normal: ''
                                        background_color: (0, 0.7, 0, 1)
                                        on_press: root.connect_to_wifi(root.selected_network, wifi_password_input.text)
                                        canvas.before:
                                            Color:
                                                rgba: (0, 0.7, 0, 1)
                                            RoundedRectangle:
                                                pos: self.pos
                                                size: self.size
                                                radius: [8]

                            # Bluetooth toggle
                            BoxLayout:
                                orientation: 'horizontal'
                                size_hint_y: None
                                height: 60
                                spacing: 20

                                Label:
                                    text: app.get_translation('Bluetooth') if app else 'Bluetooth'
                                    font_size: '18sp'
                                    size_hint_x: 0.5
                                    halign: 'left'
                                    valign: 'middle'

                                Button:
                                    text: 'ON' if root.bluetooth_enabled else 'OFF'
                                    font_size: '16sp'
                                    size_hint_x: 0.5
                                    background_normal: ''
                                    background_color: (0, 0.7, 0, 1) if root.bluetooth_enabled else (0.8, 0.3, 0.3, 1)
                                    on_press: root.toggle_bluetooth()
                                    canvas.before:
                                        Color:
                                            rgba: (0, 0.7, 0, 1) if root.bluetooth_enabled else (0.8, 0.3, 0.3, 1)
                                        RoundedRectangle:
                                            pos: self.pos
                                            size: self.size
                                            radius: [8]

                            # NMEA2000/CANBus ID setting
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 100
                                spacing: 10

                                Label:
                                    text: app.get_translation('NMEA2000/CANBus ID') if app else 'NMEA2000/CANBus ID'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                TextInput:
                                    text: root.nmea_id
                                    font_size: '16sp'
                                    size_hint_y: None
                                    height: 40
                                    multiline: False
                                    input_filter: 'int'
                                    on_text: root.set_nmea_id(self.text)
                                    background_color: 0.1, 0.2, 0.3, 1
                                    foreground_color: 1, 1, 1, 1

                        # CALIBRATION SETTINGS (visible when CALIBRATION is selected)
                        BoxLayout:
                            orientation: 'vertical'
                            size_hint_y: None
                            height: self.minimum_height if root.selected_category == 'CALIBRATION' else 0
                            opacity: 1 if root.selected_category == 'CALIBRATION' else 0
                            spacing: 15

                            # Compass calibration button
                            Button:
                                text: app.get_translation('Calibrate Compass') if app else 'Calibrate Compass'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.2, 0.4, 0.6, 1)
                                on_press: root.calibrate_compass()
                                canvas.before:
                                    Color:
                                        rgba: (0.2, 0.4, 0.6, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

                            # Water sensor zeroing
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 80

                                Label:
                                    text: app.get_translation('Water Sensor Zero Point') if app else 'Water Sensor Zero Point'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10

                                    Slider:
                                        id: water_zero_slider
                                        min: -10
                                        max: 10
                                        value: root.water_sensor_zero
                                        on_value: root.set_water_zero(self.value)
                                        size_hint_x: 0.8

                                    Label:
                                        text: f"{root.water_sensor_zero:.1f}"
                                        font_size: '16sp'
                                        size_hint_x: 0.2
                                        halign: 'center'

                            # Fuel sensor zeroing
                            BoxLayout:
                                orientation: 'vertical'
                                size_hint_y: None
                                height: 80

                                Label:
                                    text: app.get_translation('Fuel Sensor Zero Point') if app else 'Fuel Sensor Zero Point'
                                    font_size: '18sp'
                                    size_hint_y: None
                                    height: 30
                                    halign: 'left'
                                    valign: 'middle'

                                BoxLayout:
                                    orientation: 'horizontal'
                                    size_hint_y: None
                                    height: 50
                                    spacing: 10

                                    Slider:
                                        id: fuel_zero_slider
                                        min: -10
                                        max: 10
                                        value: root.fuel_sensor_zero
                                        on_value: root.set_fuel_zero(self.value)
                                        size_hint_x: 0.8

                                    Label:
                                        text: f"{root.fuel_sensor_zero:.1f}"
                                        font_size: '16sp'
                                        size_hint_x: 0.2
                                        halign: 'center'

                            # Test actuators button
                            Button:
                                text: app.get_translation('Test Actuators') if app else 'Test Actuators'
                                font_size: '16sp'
                                size_hint_y: None
                                height: 50
                                background_normal: ''
                                background_color: (0.8, 0.6, 0, 1)
                                on_press: root.test_actuators()
                                canvas.before:
                                    Color:
                                        rgba: (0.8, 0.6, 0, 1)
                                    RoundedRectangle:
                                        pos: self.pos
                                        size: self.size
                                        radius: [8]

        # Virtual Keyboard Container (shown when needed)
        BoxLayout:
            id: keyboard_container
            orientation: 'vertical'
            size_hint: 1, 0.4 if root.show_keyboard else 0
            pos_hint: {'bottom': 1}
            opacity: 1 if root.show_keyboard else 0
