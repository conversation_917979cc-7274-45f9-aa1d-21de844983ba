#:kivy 2.0.0
#:import os os

<TileButton>:
    size_hint: 1, 1
    canvas.before:
        Color:
            rgba: 0.15, 0.25, 0.4, 1
        RoundedRectangle:
            pos: self.pos
            size: self.size
            radius: [16]
    BoxLayout:
        orientation: "vertical"
        padding: [0, 16, 0, 16]
        spacing: 8
        pos: root.pos
        size: root.size
        Image:
            source: root.icon
            size_hint: None, None
            size: 64, 64
            pos_hint: {"center_x": 0.5}
        Label:
            text: root.title
            font_size: "18sp"
            font_name: "RobotoMono-Regular"
            color: 0.9, 0.95, 1, 1
            size_hint_y: None
            height: 28
            halign: "center"
            valign: "middle"
            text_size: self.size
        Label:
            text: root.value
            font_size: "16sp"
            font_name: "RobotoMono-Regular"
            color: 0.9, 0.95, 1, 1
            size_hint_y: None
            height: 28
            halign: "center"
            valign: "middle"
            text_size: self.size

<HomeScreen>:
    canvas.before:
        Color:
            rgba: 0.08, 0.12, 0.18, 1  # Tło programu - ciemny granatowy
        Rectangle:
            pos: self.pos
            size: self.size

    FloatLayout:
        # Ikony WiFi i ustawień w prawym górnym rogu
        BoxLayout:
            size_hint: None, None
            size: 48, 20
            pos_hint: {"right": 0.98, "top": 0.98}
            spacing: 6
            Image:
                source: "images/wifi.png"
                size_hint: None, None
                size: 24, 24
                allow_stretch: True
                keep_ratio: True
            Button:
                size_hint: None, None
                size: 24, 24
                background_normal: ''
                background_color: 0, 0, 0, 0
                on_press: root.go_to_settings()
                Image:
                    source: "images/settings.png"
                    size_hint: None, None
                    size: 24, 24
                    center_x: self.parent.center_x
                    center_y: self.parent.center_y
                    allow_stretch: True
                    keep_ratio: True

        # Nagłówek HOME i zegar
        BoxLayout:
            orientation: "vertical"
            size_hint: 1, None
            height: 200
            pos_hint: {"top": 1}
            padding: [0, 20, 0, 0]
            spacing: 0

            Label:
                id: home_title_label
                text: app.get_translation("HOME") if app else "HOME"
                font_size: "30sp"
                font_name: "RobotoMono-Regular"
                color: 0.8, 0.87, 0.93, 1
                size_hint_y: None
                height: 40
                halign: "center"
                valign: "middle"
                text_size: self.size

            ClockWidget:
                id: clock
                font_size: "100sp"
                font_name: "RobotoMono-Regular"
                color: 1, 1, 1, 1
                size_hint_y: None
                height: 120
                halign: "center"
                valign: "middle"
                text_size: self.size

        # Siatka kafelków - poniżej zegara
        GridLayout:
            cols: 3
            rows: 3
            spacing: [15, 15]
            padding: [30, 0, 30, 32]
            size_hint: 1, None
            height: 500  # Zwiększona wysokość
            pos_hint: {"center_x": 0.5, "top": 0.66}  # Pozycjonowanie od góry, poniżej zegara

            # LIGHTNING - First row, first column
            LightningTile:
                orientation: "vertical"
                padding: [0, 12, 0, 12]
                spacing: 8
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                Image:
                    source: "images/lightning.png"
                    size_hint: None, None
                    size: 48, 48
                    pos_hint: {"center_x": 0.5}
                Label:
                    id: lightning_label
                    text: app.get_translation("LIGHTNING") if app else "LIGHTNING"
                    font_size: "18sp"
                    font_name: "RobotoMono-Regular"
                    color: 0.9, 0.95, 1, 1
                    size_hint_y: None
                    height: 28
                    halign: "center"
                    valign: "middle"
                    text_size: self.size
                Button:
                    id: lightning_status_btn
                    text: app.get_translation("on") if app else "on"
                    font_size: "16sp"
                    font_name: "RobotoMono-Regular"
                    color: 0.9, 0.95, 1, 1
                    size_hint: None, None
                    size: 40, 28
                    pos_hint: {"center_x": 0.5}
                    background_color: 0.18, 0.56, 0.92, 1
                    canvas.before:
                        Color:
                            rgba: 0.18, 0.56, 0.92, 1
                        RoundedRectangle:
                            pos: self.pos
                            size: self.size
                            radius: [5]

            # EMERGENCY/ALARM - First row, second column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: root.go_to_alarm()  # Navigate to 'alarm' screen on release
                canvas.before:
                    Color:
                        rgba: (0.15, 0.25, 0.4, 1) if not app.has_active_alarms else ((0.8, 0.2, 0.2, 1) if app.has_critical_alarms else (0.9, 0.7, 0.2, 1))
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/emergency.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: alarm_label
                        text: app.get_translation("ALARM") if app else "ALARM"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    Label:
                        id: alarm_status_label
                        text: app.get_translation(app.alarm_status) if app else app.alarm_status
                        font_size: "18sp"
                        font_name: "RobotoMono-Regular"
                        color: (0, 1, 0, 1) if app.alarm_status == "OK" else ((1, 0, 0, 1) if app.alarm_status == "CRITICAL" else (1, 0.7, 0, 1))
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size

            # CLIMATE - First row, third column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: app.root.current = 'climate'  # Navigate to 'climate' screen on release
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/climate.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: climate_label
                        text: app.get_translation("CLIMATE") if app else "CLIMATE"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    Label:
                        text: f"{app.climate_temp:.1f}°C"
                        font_size: "18sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size

            # BATTERY - Second row, first column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: app.root.current = 'battery'  # Navigate to 'battery' screen on release
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/battery.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: battery_label
                        text: app.get_translation("BATTERY") if app else "BATTERY"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 40
                        spacing: 4
                        Label:
                            id: battery_gauge
                            text: f"{int(app.battery_level)}%"
                            font_size: "16sp"
                            font_name: "RobotoMono-Regular"
                            color: 0.9, 0.95, 1, 1
                            size_hint_y: None
                            height: 20
                            halign: "center"
                            valign: "middle"
                            text_size: self.size
                        ProgressBar:
                            id: battery_progress
                            value: app.battery_level
                            max: 100
                            size_hint_y: None
                            height: 16
                            background_color: 0.13, 0.17, 0.22, 1
                            color: 0.18, 0.56, 0.92, 1

            # ENGINE - Second row, second column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: root.go_to_engine()  # Navigate to 'engine' screen on release
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/engine.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: engine_label
                        text: app.get_translation("ENGINE") if app else "ENGINE"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    Button:
                        id: engine_status
                        text: app.get_translation(app.engine_status) if app else app.engine_status
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint: None, None
                        size: 80, 28
                        pos_hint: {"center_x": 0.5}
                        background_color: 0.18, 0.56, 0.92, 1
                        on_press: root.toggle_engine()
                        canvas.before:
                            Color:
                                rgba: 0.18, 0.56, 0.92, 1
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [5]

            # WATER - Second row, third column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: root.go_to_water()  # Navigate to 'water' screen on release
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/water.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: water_label
                        text: app.get_translation("WATER") if app else "WATER"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 40
                        spacing: 4
                        Label:
                            id: water_gauge
                            text: f"{int(app.water_level)}%"
                            font_size: "16sp"
                            font_name: "RobotoMono-Regular"
                            color: 0.9, 0.95, 1, 1
                            size_hint_y: None
                            height: 20
                            halign: "center"
                            valign: "middle"
                            text_size: self.size
                        ProgressBar:
                            id: water_progress
                            value: app.water_level
                            max: 100
                            size_hint_y: None
                            height: 16
                            background_color: 0.13, 0.17, 0.22, 1
                            color: 0.18, 0.56, 0.92, 1

            # AUTOPILOT - Third row, first column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: root.go_to_autopilot()  # Navigate to 'autopilot' screen on release
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/autopilot.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: autopilot_label
                        text: app.get_translation("AUTOPILOT") if app else "AUTOPILOT"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    Button:
                        id: autopilot_status
                        text: (app.get_translation("on") if app.autopilot else app.get_translation("off")) if app else ("on" if app.autopilot else "off")
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint: None, None
                        size: 40, 28
                        pos_hint: {"center_x": 0.5}
                        background_color: 0.18, 0.56, 0.92, 1
                        on_press: root.toggle_autopilot()
                        canvas.before:
                            Color:
                                rgba: 0.18, 0.56, 0.92, 1
                            RoundedRectangle:
                                pos: self.pos
                                size: self.size
                                radius: [5]

            # Empty space - Third row, second column
            Widget:
                canvas.before:
                    Color:
                        rgba: 0.08, 0.12, 0.18, 1  # Same as background
                    Rectangle:
                        pos: self.pos
                        size: self.size

            # FUEL - Third row, third column
            Button:
                background_color: 0, 0, 0, 0  # Make background transparent
                on_release: root.go_to_fuel()  # Navigate to 'fuel' screen on release
                canvas.before:
                    Color:
                        rgba: 0.15, 0.25, 0.4, 1
                    RoundedRectangle:
                        pos: self.pos
                        size: self.size
                        radius: [16]
                BoxLayout:
                    orientation: "vertical"
                    padding: [0, 12, 0, 12]
                    spacing: 8
                    size: self.parent.size
                    pos: self.parent.pos
                    Image:
                        source: "images/fuel.png"
                        size_hint: None, None
                        size: 48, 48
                        pos_hint: {"center_x": 0.5}
                    Label:
                        id: fuel_label
                        text: app.get_translation("FUEL") if app else "FUEL"
                        font_size: "16sp"
                        font_name: "RobotoMono-Regular"
                        color: 0.9, 0.95, 1, 1
                        size_hint_y: None
                        height: 24
                        halign: "center"
                        valign: "middle"
                        text_size: self.size
                    BoxLayout:
                        orientation: "vertical"
                        size_hint_y: None
                        height: 40
                        spacing: 4
                        Label:
                            id: fuel_gauge
                            text: f"{int(app.fuel_level)}%"
                            font_size: "16sp"
                            font_name: "RobotoMono-Regular"
                            color: 0.9, 0.95, 1, 1
                            size_hint_y: None
                            height: 20
                            halign: "center"
                            valign: "middle"
                            text_size: self.size
                        ProgressBar:
                            id: fuel_progress
                            value: app.fuel_level
                            max: 100
                            size_hint_y: None
                            height: 16
                            background_color: 0.13, 0.17, 0.22, 1
                            color: 0.18, 0.56, 0.92, 1
