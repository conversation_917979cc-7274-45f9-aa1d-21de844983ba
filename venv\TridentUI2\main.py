# Import Config przed jego użyciem
from kivy.config import Config

# Optymalizacja dla Raspberry Pi 5
import platform
import os

# Wykrywanie czy działa na Raspberry Pi
IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

# Konfiguracja grafiki zoptymalizowana dla RPi5
# FULLSCREEN WŁĄCZONY NATYCHMIAST PRZY STARCIE
if IS_RASPBERRY_PI:
    # Raspberry Pi 5 - wykorzystanie GPU VideoCore VII z natychmiastowym fullscreen
    Config.set('graphics', 'fullscreen', 'auto')  # NATYCHMIASTOWY fullscreen na RPi5
    Config.set('graphics', 'borderless', '1')     # Bez ramki okna
    Config.set('graphics', 'window_state', 'maximized')
    Config.set('graphics', 'width', '0')          # Auto-detect rozdzielczości
    Config.set('graphics', 'height', '0')         # Auto-detect rozdzielczości
    Config.set('graphics', 'resizable', '0')
    Config.set('graphics', 'maxfps', '25')        # Niższe FPS dla RPi5
    Config.set('graphics', 'vsync', '1')          # Włącz vsync na RPi5 dla płynności
    Config.set('graphics', 'multisamples', '0')   # Wyłącz antyaliasing
    Config.set('graphics', 'show_cursor', '1')
    Config.set('graphics', 'kivy_clock', 'interrupt')  # Lepszy dla ARM

    # Optymalizacje pamięci dla RPi5
    Config.set('graphics', 'texture_limit', '512')  # Limit tekstur
    Config.set('graphics', 'retain_time', '5')      # Krótszy czas przechowywania

    # Optymalizacje OpenGL ES dla RPi5
    os.environ['KIVY_GL_BACKEND'] = 'gl'
    os.environ['KIVY_WINDOW'] = 'sdl2'

    # Optymalizacje systemowe dla RPi5
    os.environ['KIVY_METRICS_DENSITY'] = '1'
    os.environ['KIVY_METRICS_FONTSCALE'] = '1'

    # Wymuś fullscreen w zmiennych środowiskowych
    os.environ['KIVY_WINDOW_FULLSCREEN'] = '1'

else:
    # Konfiguracja dla PC/innych platform - RÓWNIEŻ FULLSCREEN DOMYŚLNIE
    Config.set('graphics', 'fullscreen', 'auto')  # FULLSCREEN RÓWNIEŻ NA PC
    Config.set('graphics', 'borderless', '1')     # Bez ramki dla pełnego ekranu
    Config.set('graphics', 'width', '0')          # Auto-detect rozdzielczości
    Config.set('graphics', 'height', '0')         # Auto-detect rozdzielczości
    Config.set('graphics', 'resizable', '0')
    Config.set('graphics', 'maxfps', '30')
    Config.set('graphics', 'kivy_clock', 'free_all')
    Config.set('graphics', 'vsync', '0')
    Config.set('graphics', 'multisamples', '0')
    Config.set('graphics', 'show_cursor', '1')

    # Wymuś fullscreen w zmiennych środowiskowych
    os.environ['KIVY_WINDOW_FULLSCREEN'] = '1'

from kivy.app import App
from kivy.lang import Builder
from kivy.uix.screenmanager import ScreenManager, Screen, NoTransition
from kivy.uix.behaviors import ButtonBehavior
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.label import Label
from kivy.uix.widget import Widget
from kivy.uix.button import Button
from kivy.clock import Clock
from datetime import datetime
from kivy.properties import NumericProperty, BooleanProperty, StringProperty, ObjectProperty, ListProperty, DictProperty
from kivy.cache import Cache
import os
import json
import gc
import weakref
import time

# Import virtual keyboard
try:
    from widgets.virtual_keyboard import VirtualKeyboard, VirtualKeyboardPopup
    VIRTUAL_KEYBOARD_AVAILABLE = True
except ImportError:
    VIRTUAL_KEYBOARD_AVAILABLE = False
    print("Virtual keyboard not available")

# Import translation system
try:
    from translations import get_translation, get_available_languages, translate_dict
    from language_manager import get_language_manager, translate, set_global_language
    from translatable_screen import TranslatableScreen, TranslatableApp
    TRANSLATIONS_AVAILABLE = True
    print("✅ Translation system loaded successfully")
    print("✅ Language manager loaded successfully")
    print("✅ Translatable screen system loaded successfully")
except ImportError:
    TRANSLATIONS_AVAILABLE = False
    print("⚠️ Translation system not available")

    def get_translation(key, language="English"):
        return key

    def get_available_languages():
        return ["English", "Polski", "Deutsch", "Русский"]

    def translate(key, language=None):
        return key

    # Fallback classes if translatable_screen is not available
    class TranslatableScreen(Screen):
        pass

    class TranslatableApp:
        def __init__(self):
            pass

# Import optymalizacji dla RPi5
try:
    from config.rpi5_config import config, is_raspberry_pi
    from ui_optimizations import (
        OptimizedWidget, LazyLoadWidget, BatchUpdateMixin,
        MemoryEfficientList, perf_monitor, throttle_on_rpi,
        create_optimized_screen_manager, cleanup_memory
    )
    OPTIMIZATIONS_AVAILABLE = True
except ImportError:
    # Fallback jeśli optymalizacje nie są dostępne
    OPTIMIZATIONS_AVAILABLE = False
    IS_RASPBERRY_PI = platform.machine().startswith('arm') or 'raspberry' in platform.node().lower()

    def is_raspberry_pi():
        """Enhanced Raspberry Pi detection for all models including RPi5"""
        try:
            # Method 1: Check /proc/cpuinfo for Raspberry Pi
            try:
                with open('/proc/cpuinfo', 'r') as f:
                    cpuinfo = f.read()
                    if 'Raspberry Pi' in cpuinfo:
                        return True
                    # Check for BCM chips (RPi5 uses BCM2712)
                    if any(chip in cpuinfo for chip in ['BCM2835', 'BCM2711', 'BCM2712']):
                        return True
            except:
                pass

            # Method 2: Check device tree model
            try:
                with open('/proc/device-tree/model', 'r') as f:
                    model = f.read().strip('\x00')
                    if 'Raspberry Pi' in model:
                        return True
            except:
                pass

            # Method 3: Check platform info
            machine = platform.machine().lower()
            if machine.startswith('arm') or machine.startswith('aarch'):
                # Additional check for hostname
                hostname = platform.node().lower()
                if 'raspberry' in hostname or 'rpi' in hostname:
                    return True

            # Method 4: Check for GPIO paths (common on RPi)
            gpio_paths = ['/sys/class/gpio', '/dev/gpiomem', '/dev/gpiochip0']
            gpio_exists = any(os.path.exists(path) for path in gpio_paths)

            # If we have ARM architecture and GPIO, likely RPi
            if machine.startswith('arm') and gpio_exists:
                return True

            return False
        except Exception as e:
            print(f"Error in Raspberry Pi detection: {e}")
            return IS_RASPBERRY_PI  # Fallback to original detection

    class OptimizedWidget(Widget):
        pass

    def throttle_on_rpi(func):
        return func

    def create_optimized_screen_manager():
        return ScreenManager(transition=NoTransition())

    def cleanup_memory():
        gc.collect()

# Konfiguracja pamięci podręcznej Kivy - zoptymalizowana dla RPi5
if is_raspberry_pi():
    # Mniejsze limity pamięci podręcznej dla RPi5 (8GB RAM)
    Cache.register('kv.image', limit=100, timeout=30)      # Zmniejszone z 200
    Cache.register('kv.texture', limit=300, timeout=60)    # Zmniejszone z 1000
    Cache.register('kv.shader', limit=50, timeout=120)     # Zmniejszone z 1000
    Cache.register('kv.atlas', limit=20, timeout=300)      # Nowy cache dla atlasów
    Cache.register('app.data', limit=50, timeout=600)      # Cache dla danych aplikacji
else:
    # Większe limity dla PC
    Cache.register('kv.image', limit=200, timeout=60)
    Cache.register('kv.texture', limit=1000, timeout=120)
    Cache.register('kv.shader', limit=1000, timeout=300)
    Cache.register('kv.atlas', limit=50, timeout=600)
    Cache.register('app.data', limit=100, timeout=1200)

# Statystyki wydajności
_performance_stats = {
    'ui_updates': 0,
    'last_gc_time': time.time(),
    'gc_interval': 60.0,  # Interwał czyszczenia pamięci w sekundach
    'last_state_update': 0,
    'state_update_count': 0
}

# Import modułów do obsługi pliku system_state.json i alarmów
import state_manager
import alarm_manager
import alarm_monitor

# Import buzzer controller
try:
    from buzzer_controller import get_buzzer_controller, cleanup_buzzer
    BUZZER_AVAILABLE = True
except ImportError:
    BUZZER_AVAILABLE = False
    def get_buzzer_controller():
        return None
    def cleanup_buzzer():
        pass

# Zoptymalizowana klasa do autonomicznego wyświetlania zegara
class ClockWidget(Label):
    """
    Widget zegara, który autonomicznie aktualizuje swój czas.
    Zoptymalizowany o redukcję niepotrzebnych aktualizacji UI i zarządzanie zasobami.
    """
    def __init__(self, **kwargs):
        super(ClockWidget, self).__init__(**kwargs)
        # Przechowywanie ostatniego wyświetlonego czasu
        self._last_time = ""
        # Flaga aktywności widgetu
        self._active = True
        # Natychmiastowa aktualizacja czasu
        self.update_time(None)
        # Uruchomienie aktualizacji co 1 sekundę z opóźnieniem
        self.clock_event = Clock.schedule_interval(self.update_time, 1)

    def update_time(self, dt):
        """
        Aktualizuje wyświetlany czas tylko jeśli się zmienił i widget jest aktywny.
        Zoptymalizowana wersja z redukcją niepotrzebnych aktualizacji UI.
        """
        # Sprawdzenie, czy widget jest aktywny
        if not self._active:
            return

        # Pobranie aktualnego czasu z pamięcią podręczną
        current_time = state_manager.get_current_time()

        # Aktualizuj tekst tylko jeśli czas się zmienił
        if current_time != self._last_time:
            self.text = current_time
            self._last_time = current_time

            # Wymuszenie odświeżenia tylko tego widgetu
            self.canvas.ask_update()

    def on_parent(self, widget, parent):
        """
        Zatrzymuje aktualizację zegara, gdy widget jest usuwany z drzewa widgetów.
        Zarządza zasobami, aby uniknąć wycieków pamięci.
        """
        if parent is None:
            # Oznaczenie widgetu jako nieaktywny
            self._active = False
            # Zatrzymanie aktualizacji
            if self.clock_event:
                self.clock_event.cancel()
                self.clock_event = None

# Rejestracja ClockWidget w Factory, aby można było go używać w plikach kv
from kivy.factory import Factory
Factory.register('ClockWidget', ClockWidget)

# Rejestracja VirtualKeyboard w Factory
if VIRTUAL_KEYBOARD_AVAILABLE:
    Factory.register('VirtualKeyboard', VirtualKeyboard)
    Factory.register('VirtualKeyboardPopup', VirtualKeyboardPopup)

# Globalne zmienne do śledzenia wydajności - zoptymalizowane dla RPi5
_last_ui_update = 0
_performance_mode = True   # Tryb wydajności włączony domyślnie

# Interwały aktualizacji dostosowane do platformy
if is_raspberry_pi():
    _ui_update_interval = 0.2      # Rzadsze aktualizacje UI na RPi5 (200ms)
    _state_update_interval = 1.0   # Aktualizacja stanu co sekundę
    _alarm_check_interval = 2.0    # Sprawdzanie alarmów co 2 sekundy
    _memory_cleanup_interval = 30.0 # Czyszczenie pamięci co 30 sekund
else:
    _ui_update_interval = 0.1      # Częstsze aktualizacje na PC (100ms)
    _state_update_interval = 0.5   # Aktualizacja stanu co 500ms
    _alarm_check_interval = 1.0    # Sprawdzanie alarmów co sekundę
    _memory_cleanup_interval = 60.0 # Czyszczenie pamięci co minutę

# Liczniki wydajności
_performance_stats = {
    'ui_updates': 0,
    'state_updates': 0,
    'memory_cleanups': 0,
    'last_cleanup': 0,
    'last_gc_time': 0,
    'gc_interval': _memory_cleanup_interval
}

Builder.load_file("ui/home.kv")
Builder.load_file("ui/climate.kv")
Builder.load_file("ui/lightning.kv")
Builder.load_file("ui/battery.kv")
Builder.load_file("ui/alarm.kv")
Builder.load_file("ui/engine.kv")
Builder.load_file("ui/water.kv")
Builder.load_file("ui/fuel.kv")
Builder.load_file("ui/autopilot.kv")
Builder.load_file("ui/settings.kv")

class Tile(Screen):
    title = StringProperty()
    value = StringProperty()
    icon = StringProperty()

class LightningTile(ButtonBehavior, BoxLayout):
    def on_release(self):
        App.get_running_app().root.current = "lightning"

    def update_status(self, active):
        # Aktualizacja statusu oświetlenia w kafelku
        for child in self.walk():
            if isinstance(child, Label) and child.font_size == "16sp":
                child.text = "on" if active else "off"

class HomeScreen(TranslatableScreen):
    update_event = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Setup translations will be called automatically by TranslatableScreen

    def setup_translations(self):
        """Setup translatable elements for HomeScreen"""
        print("🌍 Setting up translations for HomeScreen")

        # Register all translatable elements
        self.register_translatable_element('home_title_label', 'HOME')
        self.register_translatable_element('lightning_label', 'LIGHTNING')
        self.register_translatable_element('alarm_label', 'ALARM')
        self.register_translatable_element('climate_label', 'CLIMATE')
        self.register_translatable_element('battery_label', 'BATTERY')
        self.register_translatable_element('engine_label', 'ENGINE')
        self.register_translatable_element('water_label', 'WATER')
        self.register_translatable_element('autopilot_label', 'AUTOPILOT')

        print("✅ HomeScreen translations setup completed")

    def on_enter(self):
        print("🏠 *** HOMESCREEN ON_ENTER CALLED ***")
        print("=== WEJŚCIE NA EKRAN HOME ===")

        # Call parent on_enter (which handles translation updates)
        super().on_enter()

        # Aktualizacja danych przy wejściu na ekran
        print("Wymuszenie aktualizacji UI przy wejściu na ekran")
        self.update_from_state(None)

        # Uruchomienie aktualizacji z interwałem dostosowanym do platformy
        self.update_event = Clock.schedule_interval(self.update_from_state, _state_update_interval)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN HOME ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None



    def update_from_state(self, dt=None):
        """
        Aktualizuje UI na podstawie danych z aplikacji.
        Zoptymalizowana wersja, która aktualizuje tylko zmienione wartości.
        """
        try:
            # Pobieranie danych z aplikacji
            app = App.get_running_app()

            # Przechowywanie informacji o zmianach
            ui_changed = False

            # Aktualizacja wartości wskaźników tylko jeśli się zmieniły
            if hasattr(self.ids, 'battery_gauge'):
                new_text = f"{int(app.battery_level)}%"
                if self.ids.battery_gauge.text != new_text:
                    self.ids.battery_gauge.text = new_text
                    ui_changed = True

            if hasattr(self.ids, 'water_gauge'):
                new_text = f"{int(app.water_level)}%"
                if self.ids.water_gauge.text != new_text:
                    self.ids.water_gauge.text = new_text
                    ui_changed = True

            if hasattr(self.ids, 'fuel_gauge'):
                new_text = f"{int(app.fuel_level)}%"
                if self.ids.fuel_gauge.text != new_text:
                    self.ids.fuel_gauge.text = new_text
                    ui_changed = True

            # Aktualizacja pasków postępu
            if hasattr(self.ids, 'battery_progress'):
                if self.ids.battery_progress.value != app.battery_level:
                    self.ids.battery_progress.value = app.battery_level
                    ui_changed = True

            if hasattr(self.ids, 'water_progress'):
                if self.ids.water_progress.value != app.water_level:
                    self.ids.water_progress.value = app.water_level
                    ui_changed = True

            if hasattr(self.ids, 'fuel_progress'):
                if self.ids.fuel_progress.value != app.fuel_level:
                    self.ids.fuel_progress.value = app.fuel_level
                    ui_changed = True

            # Aktualizacja stanu przycisków
            if hasattr(self.ids, 'engine_status'):
                if self.ids.engine_status.text != app.engine_status:
                    self.ids.engine_status.text = app.engine_status
                    ui_changed = True

            if hasattr(self.ids, 'autopilot_status'):
                new_text = "on" if app.autopilot else "off"
                if self.ids.autopilot_status.text != new_text:
                    self.ids.autopilot_status.text = new_text
                    ui_changed = True

            # Aktualizacja kafelka oświetlenia
            any_light_active = (app.interior_light_active or
                               app.navigation_light_active or
                               app.deck_light_active)

            # Szukamy kafelka LightningTile i aktualizujemy jego status tylko jeśli się zmienił
            for child in self.walk():
                if isinstance(child, LightningTile):
                    # Sprawdzamy aktualny stan
                    current_state = None
                    for label_child in child.walk():
                        if isinstance(label_child, Label) and label_child.font_size == "16sp":
                            current_state = label_child.text == "on"
                            break

                    # Aktualizujemy tylko jeśli stan się zmienił
                    if current_state != any_light_active:
                        child.update_status(any_light_active)
                        ui_changed = True
                    break

            # Wymuszenie odświeżenia widoku tylko jeśli coś się zmieniło
            if ui_changed:
                self.canvas.ask_update()

        except Exception as e:
            print(f"Błąd aktualizacji UI w HomeScreen: {e}")
            import traceback
            traceback.print_exc()

    def toggle_engine(self):
        app = App.get_running_app()
        if app.engine_status == "active":
            app.engine_status = "inactive"
        else:
            app.engine_status = "active"
        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def toggle_autopilot(self):
        app = App.get_running_app()

        # Jeśli włączamy autopilota, upewnij się, że silnik jest aktywny
        if not app.autopilot and app.engine_status != "active":
            app.engine_status = "active"

        app.autopilot = not app.autopilot

        # Jeśli wyłączamy autopilota, nie wyłączaj silnika

        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def go_to_climate(self):
        self.manager.current = 'climate'

    def go_to_lightning(self):
        self.manager.current = 'lightning'

    def go_to_battery(self):
        self.manager.current = 'battery'

    def go_to_alarm(self):
        self.manager.current = 'alarm'

    def go_to_engine(self):
        self.manager.current = 'engine'

    def go_to_water(self):
        self.manager.current = 'water'

    def go_to_fuel(self):
        self.manager.current = 'fuel'

    def go_to_autopilot(self):
        self.manager.current = 'autopilot'

    def go_to_settings(self):
        self.manager.current = 'settings'

class ClimateScreen(TranslatableScreen):
    set_temp = NumericProperty(22)
    fridge_temp = NumericProperty(4)
    auto_ac = BooleanProperty(True)
    fan_power = NumericProperty(50)
    ac_mode = StringProperty("off")
    current_internal_temp = NumericProperty(22)
    update_event = None

    def setup_translations(self):
        """Setup translatable elements for ClimateScreen"""
        print("🌍 Setting up translations for ClimateScreen")
        # Add any translatable elements here if needed
        print("✅ ClimateScreen translations setup completed")

    def on_pre_enter(self):
        app = App.get_running_app()
        self.set_temp = app.climate_temp
        self.fridge_temp = app.fridge_temp
        self.auto_ac = app.auto_ac
        self.fan_power = app.fan_power
        self.ac_mode = app.ac_mode
        self.current_internal_temp = app.current_internal_temp

        # Uruchomienie aktualizacji z interwałem dostosowanym do platformy
        self.update_event = Clock.schedule_interval(self.update_from_state, _state_update_interval)

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja lokalnych właściwości na podstawie centralnego stanu
        app = App.get_running_app()
        self.set_temp = app.climate_temp
        self.fridge_temp = app.fridge_temp
        self.auto_ac = app.auto_ac
        self.fan_power = app.fan_power
        self.ac_mode = app.ac_mode
        self.current_internal_temp = app.current_internal_temp

        # Aktualizacja UI
        try:
            # Aktualizacja suwaka mocy wentylatora
            if hasattr(self.ids, 'fan_power_slider'):
                self.ids.fan_power_slider.value = self.fan_power

            # Aktualizacja statusu systemu
            if hasattr(self.ids, 'system_status'):
                if self.ac_mode == "heating":
                    self.ids.system_status.text = "HEATING"
                    self.ids.system_status.color = (1, 0.5, 0, 1)  # Pomarańczowy dla ogrzewania
                elif self.ac_mode == "cooling":
                    self.ids.system_status.text = "COOLING"
                    self.ids.system_status.color = (0, 0.7, 1, 1)  # Niebieski dla chłodzenia
                else:
                    self.ids.system_status.text = "OK"
                    self.ids.system_status.color = (0, 1, 0, 1)  # Zielony dla OK

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI klimatyzacji: {e}")

    def increase_temp(self):
        self.set_temp += 1
        app = App.get_running_app()
        app.climate_temp = self.set_temp
        app.save_state_to_file()

    def decrease_temp(self):
        self.set_temp -= 1
        app = App.get_running_app()
        app.climate_temp = self.set_temp
        app.save_state_to_file()

    def increase_fridge_temp(self):
        self.fridge_temp += 1
        app = App.get_running_app()
        app.fridge_temp = self.fridge_temp
        app.save_state_to_file()

    def decrease_fridge_temp(self):
        self.fridge_temp -= 1
        app = App.get_running_app()
        app.fridge_temp = self.fridge_temp
        app.save_state_to_file()

    def toggle_auto_ac(self):
        self.auto_ac = not self.auto_ac
        app = App.get_running_app()
        app.auto_ac = self.auto_ac
        app.save_state_to_file()

    def set_fan_power(self, value):
        # Aktualizacja mocy wentylatora tylko gdy auto_ac jest wyłączone
        if not self.auto_ac:
            self.fan_power = value
            app = App.get_running_app()
            app.fan_power = self.fan_power
            app.save_state_to_file()

class LightningScreen(TranslatableScreen):
    interior_r = NumericProperty(255)
    interior_g = NumericProperty(255)
    interior_b = NumericProperty(255)
    interior_active = BooleanProperty(False)
    navigation_active = BooleanProperty(False)
    deck_active = BooleanProperty(False)
    interior_intensity = NumericProperty(100)  # domyślna wartość 100%

    def setup_translations(self):
        """Setup translatable elements for LightningScreen"""
        print("🌍 Setting up translations for LightningScreen")

        # Register translatable elements from lightning.kv
        # Note: These will need to be updated in the .kv file to use IDs
        # For now, we'll register them for future use
        self.register_translatable_element('navigation_label', 'NAVIGATION LIGHTNING')
        self.register_translatable_element('interior_label', 'INTERIOR LIGHTNING')
        self.register_translatable_element('deck_label', 'DECK LIGHTNING')
        self.register_translatable_element('system_status_label', 'SYSTEM STATUS')
        self.register_translatable_element('red_label', 'Red')
        self.register_translatable_element('green_label', 'Green')
        self.register_translatable_element('blue_label', 'Blue')
        self.register_translatable_element('intensity_label', 'Intensity')
        self.register_translatable_element('go_home_btn', 'Go to Home')

        print("✅ LightningScreen translations setup completed")

    def on_pre_enter(self):
        # Synchronizacja stanu z centralnym stanem
        app = App.get_running_app()
        self.interior_active = app.interior_light_active
        self.navigation_active = app.navigation_light_active
        self.deck_active = app.deck_light_active
        self.interior_r = app.interior_r
        self.interior_g = app.interior_g
        self.interior_b = app.interior_b
        self.interior_intensity = app.interior_intensity

        # Aktualizacja tekstu przycisków
        if hasattr(self.ids, 'interior_btn'):
            self.ids.interior_btn.text = 'on' if self.interior_active else 'off'
        if hasattr(self.ids, 'navigation_btn'):
            self.ids.navigation_btn.text = 'on' if self.navigation_active else 'off'
        if hasattr(self.ids, 'deck_btn'):
            self.ids.deck_btn.text = 'on' if self.deck_active else 'off'

    def update_from_state(self, dt=None):
        # Synchronizacja stanu z centralnym stanem
        app = App.get_running_app()

        # Zapisanie poprzednich wartości, aby wykryć zmiany
        old_interior_active = self.interior_active
        old_navigation_active = self.navigation_active
        old_deck_active = self.deck_active

        # Aktualizacja właściwości
        self.interior_active = app.interior_light_active
        self.navigation_active = app.navigation_light_active
        self.deck_active = app.deck_light_active
        self.interior_r = app.interior_r
        self.interior_g = app.interior_g
        self.interior_b = app.interior_b
        self.interior_intensity = app.interior_intensity

        # Aktualizacja tekstu przycisków
        if hasattr(self.ids, 'interior_btn'):
            self.ids.interior_btn.text = 'on' if self.interior_active else 'off'
        if hasattr(self.ids, 'navigation_btn'):
            self.ids.navigation_btn.text = 'on' if self.navigation_active else 'off'
        if hasattr(self.ids, 'deck_btn'):
            self.ids.deck_btn.text = 'on' if self.deck_active else 'off'



        # Aktualizacja sliderów RGB i intensywności
        if hasattr(self.ids, 'interior_r'):
            self.ids.interior_r.value = self.interior_r
        if hasattr(self.ids, 'interior_g'):
            self.ids.interior_g.value = self.interior_g
        if hasattr(self.ids, 'interior_b'):
            self.ids.interior_b.value = self.interior_b
        if hasattr(self.ids, 'interior_intensity'):
            self.ids.interior_intensity.value = self.interior_intensity

        # Wymuszenie odświeżenia widoku
        self.canvas.ask_update()

        # Jeśli zmieniły się stany oświetlenia, wymuszamy pełne odświeżenie widoku
        if (old_interior_active != self.interior_active or
            old_navigation_active != self.navigation_active or
            old_deck_active != self.deck_active):
            print(f"Zmiana stanu oświetlenia: interior={self.interior_active}, navigation={self.navigation_active}, deck={self.deck_active}")
            # Wymuszenie pełnego odświeżenia widoku
            self._trigger_layout()

    def on_interior_light(self):
        self.interior_active = not self.interior_active
        self.ids.interior_btn.text = 'on' if self.interior_active else 'off'

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.interior_light_active = self.interior_active
        app.save_state_to_file()

    def on_navigation_light(self):
        self.navigation_active = not self.navigation_active
        self.ids.navigation_btn.text = 'on' if self.navigation_active else 'off'

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.navigation_light_active = self.navigation_active
        app.save_state_to_file()

    def on_deck_light(self):
        self.deck_active = not self.deck_active
        self.ids.deck_btn.text = 'on' if self.deck_active else 'off'

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.deck_light_active = self.deck_active
        app.save_state_to_file()

    def update_interior_color(self):
        # This method will be called when RGB sliders change
        print(f"Interior RGB: {self.interior_r}, {self.interior_g}, {self.interior_b}")
        print(f"Intensity: {self.interior_intensity}%")

        # Aktualizacja centralnego stanu
        app = App.get_running_app()
        app.interior_r = self.interior_r
        app.interior_g = self.interior_g
        app.interior_b = self.interior_b
        app.interior_intensity = self.interior_intensity
        app.save_state_to_file()

    def go_to_home(self):
        self.manager.current = 'home'

class AlarmItem(BoxLayout):
    """Klasa reprezentująca pojedynczy alarm w interfejsie użytkownika"""
    alarm_id = StringProperty("")
    alarm_type = StringProperty("critical")  # critical, warning, info
    alarm_message = StringProperty("")
    alarm_timestamp = StringProperty("")
    alarm_source = StringProperty("")
    alarm_acknowledged = BooleanProperty(False)
    alarm_active = BooleanProperty(True)

    def __init__(self, **kwargs):
        super(AlarmItem, self).__init__(**kwargs)
        print(f"Tworzenie AlarmItem: {self.alarm_message} ({self.alarm_type})")


class BatteryScreen(TranslatableScreen):
    sim_update_event = None

    def setup_translations(self):
        """Setup translatable elements for BatteryScreen"""
        print("🌍 Setting up translations for BatteryScreen")
        # Add any translatable elements here if needed
        print("✅ BatteryScreen translations setup completed")

    def on_pre_enter(self):
        # Natychmiastowa aktualizacja UI
        self.update_from_state(0)

        # Uruchomienie aktualizacji UI z interwałem dostosowanym do platformy
        # RPi5: rzadsze aktualizacje dla oszczędności CPU
        self.sim_update_event = Clock.schedule_interval(self.update_from_state, _ui_update_interval)

    def on_leave(self):
        # Zatrzymanie symulacji przy wyjściu z ekranu
        if self.sim_update_event:
            self.sim_update_event.cancel()
            self.sim_update_event = None

    def update_from_state(self, dt=None):
        # Odświeżenie UI na podstawie danych z centralnego stanu
        try:
            app = App.get_running_app()



            # Aktualizacja wskaźników baterii
            if hasattr(self.ids, 'battery_level_label'):
                self.ids.battery_level_label.text = f"{int(app.battery_level)}%"

            # Aktualizacja statusu ładowania
            if hasattr(self.ids, 'charging_status'):
                self.ids.charging_status.text = 'CHARGING' if app.charging else 'DISCHARGING'
                self.ids.charging_status.color = (0, 0.8, 0, 1) if app.charging else (0.8, 0.3, 0.3, 1)

            # Aktualizacja przycisków źródła zasilania
            for child in self.walk():
                # Sprawdzamy, czy element jest przyciskiem ToggleButton
                if hasattr(child, 'group') and child.group == 'power_source' and hasattr(child, 'text'):
                    # Aktualizujemy stan przycisku na podstawie aktualnego źródła zasilania
                    if child.text == app.power_source:
                        child.state = 'down'
                    else:
                        child.state = 'normal'

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI baterii: {e}")
            import traceback
            traceback.print_exc()

    def change_power_source(self, source):
        # Zmiana źródła zasilania w centralnym stanie
        print(f"=== ROZPOCZĘCIE ZMIANY ŹRÓDŁA ZASILANIA W GŁÓWNEJ APLIKACJI NA {source} ===")

        app = App.get_running_app()

        # Zapisanie aktualnej wartości przed zmianą
        old_power_source = app.power_source
        print(f"Aktualne źródło zasilania: {old_power_source}")

        # Zmiana źródła zasilania
        app.power_source = source
        print(f"Nowe źródło zasilania: {app.power_source}")

        # Aktualizacja stanu ładowania na podstawie źródła zasilania
        if source == "Shore Power" or source == "Solar Panels":
            app.charging = True
        else:
            app.charging = False

        print(f"Stan ładowania: {app.charging}")

        # Zapisanie stanu do pliku
        app.save_state_to_file()

        print(f"Zmieniono źródło zasilania z {old_power_source} na {source}")
        print(f"=== ZAKOŃCZENIE ZMIANY ŹRÓDŁA ZASILANIA W GŁÓWNEJ APLIKACJI ===")

    def go_to_home(self):
        self.manager.current = 'home'

class EngineScreen(TranslatableScreen):
    update_event = None

    def setup_translations(self):
        """Setup translatable elements for EngineScreen"""
        print("🌍 Setting up translations for EngineScreen")

        # Register translatable elements from engine.kv
        self.register_translatable_element('engine_title', 'ENGINE CONTROL')
        self.register_translatable_element('engine_status_title', 'ENGINE STATUS')
        self.register_translatable_element('throttle_title', 'THROTTLE CONTROL')
        self.register_translatable_element('engine_params_title', 'ENGINE PARAMETERS')
        self.register_translatable_element('start_stop_btn', 'START')  # This will change dynamically
        self.register_translatable_element('rpm_label', 'RPM')
        self.register_translatable_element('temp_label', 'Temperature')
        self.register_translatable_element('oil_pressure_label', 'Oil Pressure')
        self.register_translatable_element('engine_hours_label', 'Engine Hours')
        self.register_translatable_element('system_status_label', 'SYSTEM STATUS')

        print("✅ EngineScreen translations setup completed")

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN ENGINE ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN ENGINE ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja statusu silnika
            if hasattr(self.ids, 'engine_status_label'):
                self.ids.engine_status_label.text = app.engine_status.upper()
                self.ids.engine_status_label.color = (0, 0.8, 0, 1) if app.engine_status == "active" else (0.8, 0.3, 0.3, 1)

            # Aktualizacja wartości przepustnicy (throttle)
            if hasattr(self.ids, 'throttle_slider'):
                self.ids.throttle_slider.value = app.engine_throttle

            # Aktualizacja RPM na podstawie przepustnicy
            if hasattr(self.ids, 'rpm_label'):
                # RPM zależy od przepustnicy i statusu silnika
                rpm = 0
                if app.engine_status == "active":
                    # Obliczenie RPM na podstawie przepustnicy (0-100%)
                    # Zakres RPM: 800 (bieg jałowy) do 3500 (maksymalne obroty)
                    rpm = 800 + (app.engine_throttle / 100) * 2700
                self.ids.rpm_label.text = f"{int(rpm)}"

                # Zmiana koloru RPM w zależności od wartości
                if hasattr(self.ids, 'rpm_label'):
                    if rpm > 3000:
                        self.ids.rpm_label.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla wysokich obrotów
                    elif rpm > 2500:
                        self.ids.rpm_label.color = (0.9, 0.7, 0.2, 1)  # Żółty dla średnio-wysokich obrotów
                    else:
                        self.ids.rpm_label.color = (0.3, 0.8, 0.3, 1)  # Zielony dla normalnych obrotów

            # Aktualizacja temperatury silnika
            if hasattr(self.ids, 'temp_label'):
                self.ids.temp_label.text = f"{app.engine_temperature:.1f}°C"

                # Zmiana koloru temperatury w zależności od wartości
                if app.engine_temperature > 100:
                    self.ids.temp_label.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla wysokiej temperatury

                    # Sprawdzenie czy istnieje alarm dla wysokiej temperatury silnika
                    if app.engine_temperature > 110 and app.alarm_manager_instance:
                        # Dodanie alarmu o wysokiej temperaturze silnika
                        app.add_alarm("critical", f"WARNING: Engine temperature critically high at {app.engine_temperature:.1f}°C!", "Engine")
                elif app.engine_temperature > 90:
                    self.ids.temp_label.color = (0.9, 0.7, 0.2, 1)  # Żółty dla podwyższonej temperatury
                else:
                    self.ids.temp_label.color = (0.3, 0.8, 0.3, 1)  # Zielony dla normalnej temperatury

            # Aktualizacja godzin pracy silnika
            if hasattr(self.ids, 'hours_label'):
                self.ids.hours_label.text = f"{int(app.engine_hours)} h"

            # Aktualizacja ciśnienia oleju (symulowane na podstawie RPM)
            if hasattr(self.ids, 'oil_pressure_label'):
                oil_pressure = 0
                if app.engine_status == "active":
                    # Ciśnienie oleju zależy od obrotów silnika
                    rpm = 800 + (app.engine_throttle / 100) * 2700
                    oil_pressure = 2.0 + (rpm / 3500) * 3.5
                self.ids.oil_pressure_label.text = f"{oil_pressure:.1f} bar"

                # Zmiana koloru ciśnienia oleju w zależności od wartości
                if oil_pressure < 2.0 and app.engine_status == "active":
                    self.ids.oil_pressure_label.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla niskiego ciśnienia

                    # Dodanie alarmu o niskim ciśnieniu oleju
                    if app.alarm_manager_instance:
                        app.add_alarm("warning", f"WARNING: Low oil pressure at {oil_pressure:.1f} bar!", "Engine")
                elif oil_pressure > 5.0:
                    self.ids.oil_pressure_label.color = (0.9, 0.7, 0.2, 1)  # Żółty dla wysokiego ciśnienia
                else:
                    self.ids.oil_pressure_label.color = (0.3, 0.8, 0.3, 1)  # Zielony dla normalnego ciśnienia

            # Aktualizacja statusu systemu
            if hasattr(self.ids, 'system_status'):
                if app.engine_temperature > 100:
                    self.ids.system_status.text = "HIGH TEMP"
                    self.ids.system_status.color = (0.9, 0.2, 0.2, 1)  # Czerwony dla wysokiej temperatury
                elif app.engine_status == "active" and app.engine_throttle > 80:
                    self.ids.system_status.text = "HIGH RPM"
                    self.ids.system_status.color = (0.9, 0.7, 0.2, 1)  # Żółty dla wysokich obrotów
                else:
                    self.ids.system_status.text = "OK"
                    self.ids.system_status.color = (0, 1, 0, 1)  # Zielony dla OK

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI silnika: {e}")
            import traceback
            traceback.print_exc()

    def toggle_engine(self):
        app = App.get_running_app()
        if app.engine_status == "active":
            app.engine_status = "inactive"
            # Reset throttle when engine is turned off
            app.engine_throttle = 0

            # Dodanie alarmu o wyłączeniu silnika
            if app.alarm_manager_instance:
                app.add_alarm("info", "Engine stopped", "Engine")
        else:
            app.engine_status = "active"
            # Dodanie alarmu o uruchomieniu silnika
            if app.alarm_manager_instance:
                app.add_alarm("info", "Engine started", "Engine")

            # Sprawdzenie poziomu paliwa przy uruchomieniu silnika
            if app.fuel_level < 15 and app.alarm_manager_instance:
                app.add_alarm("critical", f"CRITICAL: Engine running with critically low fuel ({int(app.fuel_level)}%)! Engine may stop unexpectedly.", "Engine")

            # Zwiększenie licznika godzin pracy silnika
            app.engine_hours += 0.1

        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def set_throttle(self, value):
        app = App.get_running_app()
        # Only allow throttle changes when engine is active
        if app.engine_status == "active":
            app.engine_throttle = value

            # Symulacja zmiany temperatury silnika w zależności od przepustnicy
            # Temperatura rośnie wraz ze wzrostem przepustnicy
            base_temp = 65.0  # Temperatura bazowa przy biegu jałowym
            max_temp_increase = 45.0  # Maksymalny wzrost temperatury przy pełnej przepustnicy
            app.engine_temperature = base_temp + (value / 100.0) * max_temp_increase

            app.save_state_to_file()

            # Natychmiastowa aktualizacja UI
            self.update_from_state(None)

    def go_to_home(self):
        self.manager.current = 'home'


class WaterScreen(TranslatableScreen):
    update_event = None
    water_pump_active = BooleanProperty(False)

    def setup_translations(self):
        """Setup translatable elements for WaterScreen"""
        print("🌍 Setting up translations for WaterScreen")
        # Register translatable elements if any exist in the UI
        print("✅ WaterScreen translations setup completed")

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN WATER ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN WATER ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja poziomu wody
            if hasattr(self.ids, 'water_level_label'):
                self.ids.water_level_label.text = f"{int(app.water_level)}%"

            if hasattr(self.ids, 'water_level_bar'):
                self.ids.water_level_bar.value = app.water_level

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI wody: {e}")
            import traceback
            traceback.print_exc()

    def toggle_water_pump(self):
        self.water_pump_active = not self.water_pump_active
        # Aktualizacja UI
        self.update_from_state(None)

    def go_to_home(self):
        self.manager.current = 'home'


class FuelScreen(TranslatableScreen):
    update_event = None

    def setup_translations(self):
        """Setup translatable elements for FuelScreen"""
        print("🌍 Setting up translations for FuelScreen")
        # Register translatable elements if any exist in the UI
        print("✅ FuelScreen translations setup completed")

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN FUEL ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN FUEL ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja poziomu paliwa
            if hasattr(self.ids, 'fuel_level_label'):
                self.ids.fuel_level_label.text = f"{int(app.fuel_level)}%"

            if hasattr(self.ids, 'fuel_level_bar'):
                self.ids.fuel_level_bar.value = app.fuel_level

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI paliwa: {e}")
            import traceback
            traceback.print_exc()

    def go_to_home(self):
        self.manager.current = 'home'


class AutopilotScreen(TranslatableScreen):
    update_event = None
    heading = NumericProperty(270)

    def setup_translations(self):
        """Setup translatable elements for AutopilotScreen"""
        print("🌍 Setting up translations for AutopilotScreen")
        # Register translatable elements if any exist in the UI
        print("✅ AutopilotScreen translations setup completed")

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN AUTOPILOT ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN AUTOPILOT ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        # Aktualizacja UI na podstawie danych z aplikacji
        try:
            app = App.get_running_app()

            # Aktualizacja statusu autopilota
            if hasattr(self.ids, 'autopilot_status_label'):
                self.ids.autopilot_status_label.text = "ACTIVE" if app.autopilot else "INACTIVE"
                self.ids.autopilot_status_label.color = (0, 0.8, 0, 1) if app.autopilot else (0.8, 0.3, 0.3, 1)

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
        except Exception as e:
            print(f"Błąd aktualizacji UI autopilota: {e}")
            import traceback
            traceback.print_exc()

    def toggle_autopilot(self):
        app = App.get_running_app()

        # Jeśli włączamy autopilota, upewnij się, że silnik jest aktywny
        if not app.autopilot and app.engine_status != "active":
            app.engine_status = "active"

        app.autopilot = not app.autopilot

        # Jeśli wyłączamy autopilota, nie wyłączaj silnika

        app.save_state_to_file()

        # Natychmiastowa aktualizacja UI
        self.update_from_state(None)

    def adjust_heading(self, change):
        self.heading = (self.heading + change) % 360
        # W rzeczywistej aplikacji, tutaj byłaby aktualizacja centralnego stanu

    def go_to_home(self):
        self.manager.current = 'home'


class SettingsScreen(TranslatableScreen):
    """
    Settings screen for PoseidonBox yacht control system.
    Provides comprehensive settings management across 6 categories:
    DISPLAY, LANGUAGE, SYSTEM, SAFETY, CONNECTION, CALIBRATION
    """

    # Current selected category
    selected_category = StringProperty("DISPLAY")

    # Display settings
    brightness = NumericProperty(80)
    theme_mode = StringProperty("Night")  # Day/Night
    screen_orientation = StringProperty("Landscape")

    # Language settings
    language = StringProperty("English")
    time_format = StringProperty("24h")  # 12h/24h
    units = StringProperty("Metric")  # Metric/Imperial

    # Available languages
    available_languages = ListProperty(["English", "Polski", "Deutsch", "Русский"])

    # System settings
    firmware_version = StringProperty("TridentOS v2.1.0")

    # Safety settings
    settings_lock_enabled = BooleanProperty(False)
    emergency_contact = StringProperty("VHF Channel 16")
    secure_mode = BooleanProperty(False)
    pin_code = StringProperty("")
    pin_enabled = BooleanProperty(False)

    # Connection settings
    wifi_enabled = BooleanProperty(True)
    bluetooth_enabled = BooleanProperty(True)
    nmea_id = StringProperty("001")
    available_networks = ListProperty([])
    connected_network = StringProperty("")
    wifi_password = StringProperty("")
    show_keyboard = BooleanProperty(False)
    selected_network = StringProperty("")

    # Calibration settings
    water_sensor_zero = NumericProperty(0)
    fuel_sensor_zero = NumericProperty(0)

    def __init__(self, **kwargs):
        super(SettingsScreen, self).__init__(**kwargs)
        print("🔧 SettingsScreen __init__ called")
        self.current_keyboard = None
        self.load_settings()
        # Initialize WiFi networks list
        Clock.schedule_once(self.delayed_init, 0.1)
        # Test language system
        self.test_language_system_init()

    def delayed_init(self, dt):
        """Initialize components that need the UI to be ready"""
        try:
            # Initialize WiFi networks if needed
            if not self.available_networks:
                self.scan_wifi_networks()
        except Exception as e:
            print(f"Error in delayed_init: {e}")

    def test_language_system_init(self):
        """Test language system during initialization"""
        try:
            print("🧪 Testing language system during init...")
            print(f"🔍 TRANSLATIONS_AVAILABLE: {TRANSLATIONS_AVAILABLE}")
            print(f"🔍 Current language: {self.language}")

            if TRANSLATIONS_AVAILABLE:
                manager = get_language_manager()
                print(f"✅ Language manager available")
                print(f"🔍 Manager current language: {manager.get_current_language()}")

                # Test a simple translation
                test_translation = manager.get_translation("HOME")
                print(f"🔍 Test translation HOME: {test_translation}")
            else:
                print("⚠️ Translation system not available during init")

        except Exception as e:
            print(f"❌ Error testing language system during init: {e}")
            import traceback
            traceback.print_exc()

    def on_enter(self):
        """Called when entering the settings screen"""
        print("=== WEJŚCIE NA EKRAN SETTINGS ===")
        self.load_settings()
        # Update networks list UI
        self.update_networks_ui()

    def load_settings(self):
        """Load settings from persistent storage"""
        try:
            import json
            import os

            settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, "r") as f:
                    settings_data = json.load(f)

                # Load display settings
                display = settings_data.get('display', {})
                self.brightness = display.get('brightness', 80)
                self.theme_mode = display.get('theme_mode', 'Night')
                self.screen_orientation = display.get('orientation', 'Landscape')

                # Load language settings
                language = settings_data.get('language', {})
                self.language = language.get('language', 'English')
                self.time_format = language.get('time_format', '24h')
                self.units = language.get('units', 'Metric')

                # Load safety settings
                safety = settings_data.get('safety', {})
                self.settings_lock_enabled = safety.get('lock_enabled', False)
                self.emergency_contact = safety.get('emergency_contact', 'VHF Channel 16')
                self.secure_mode = safety.get('secure_mode', False)
                self.pin_code = safety.get('pin_code', '')
                self.pin_enabled = safety.get('pin_enabled', False)

                # Load connection settings
                connection = settings_data.get('connection', {})
                self.wifi_enabled = connection.get('wifi_enabled', True)
                self.bluetooth_enabled = connection.get('bluetooth_enabled', True)
                self.nmea_id = connection.get('nmea_id', '001')

                # Load calibration settings
                calibration = settings_data.get('calibration', {})
                self.water_sensor_zero = calibration.get('water_zero', 0)
                self.fuel_sensor_zero = calibration.get('fuel_zero', 0)

        except Exception as e:
            print(f"Error loading settings: {e}")

    def save_settings(self):
        """Save settings to persistent storage"""
        try:
            import json
            import os

            settings_data = {
                'display': {
                    'brightness': self.brightness,
                    'theme_mode': self.theme_mode,
                    'orientation': self.screen_orientation
                },
                'language': {
                    'language': self.language,
                    'time_format': self.time_format,
                    'units': self.units
                },
                'safety': {
                    'lock_enabled': self.settings_lock_enabled,
                    'emergency_contact': self.emergency_contact,
                    'secure_mode': self.secure_mode,
                    'pin_code': self.pin_code,
                    'pin_enabled': self.pin_enabled
                },
                'connection': {
                    'wifi_enabled': self.wifi_enabled,
                    'bluetooth_enabled': self.bluetooth_enabled,
                    'nmea_id': self.nmea_id
                },
                'calibration': {
                    'water_zero': self.water_sensor_zero,
                    'fuel_zero': self.fuel_sensor_zero
                }
            }

            settings_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "settings.json")
            with open(settings_file, "w") as f:
                json.dump(settings_data, f, indent=2)

            print("Settings saved successfully")

        except Exception as e:
            print(f"Error saving settings: {e}")

    def select_category(self, category):
        """Select a settings category"""
        print(f"🔍 CATEGORY BUTTON CLICKED: {category}")
        print(f"🔍 Current selected_category: {self.selected_category}")
        self.selected_category = category
        print(f"🔍 New selected_category: {self.selected_category}")
        print(f"✅ Category selection completed")

    def set_brightness(self, value):
        """Set display brightness"""
        self.brightness = value
        self.save_settings()
        # Apply brightness to system (for Raspberry Pi)
        self.apply_brightness(value)

    def toggle_theme(self):
        """Toggle between Day and Night theme"""
        self.theme_mode = "Day" if self.theme_mode == "Night" else "Night"
        self.save_settings()
        self.apply_theme()

    def set_orientation(self, orientation):
        """Set screen orientation"""
        self.screen_orientation = orientation
        self.save_settings()

    def set_language(self, language):
        """Set system language with global UI update using new translation system"""
        print(f"🌍 Setting language to: {language}")

        # Use new global language system
        if TRANSLATIONS_AVAILABLE:
            try:
                # Use global language setter which triggers all UI updates
                success = set_global_language(language)

                if not success:
                    print(f"❌ Failed to set language to: {language}")
                    return False

                # Update local language setting
                old_language = self.language
                self.language = language
                self.save_settings()

                print(f"✅ Language changed from '{old_language}' to '{language}' using global system")
                return True

            except Exception as e:
                print(f"❌ Error using global language system: {e}")
                import traceback
                traceback.print_exc()

        # Fallback method if global language system not available
        old_language = self.language
        self.language = language
        self.save_settings()

        print(f"✅ Language changed from '{old_language}' to '{language}' (fallback)")
        return True

    def force_language_ui_refresh(self):
        """Force UI refresh specifically for language changes"""
        try:
            print(f"🔄 Forcing language UI refresh on SettingsScreen...")

            # Force property update to trigger UI refresh
            self.property('language').dispatch(self)

            # Get app instance and force its refresh too
            try:
                app = App.get_running_app()
                if hasattr(app, 'force_language_ui_refresh'):
                    app.force_language_ui_refresh()
            except Exception as e:
                print(f"⚠️ Could not trigger app-level language refresh: {e}")

            # Force canvas update
            if hasattr(self, 'canvas'):
                self.canvas.ask_update()

            print(f"✅ SettingsScreen language UI refresh completed")

        except Exception as e:
            print(f"❌ Error forcing language UI refresh on SettingsScreen: {e}")



    # Individual language setter functions for better .kv compatibility
    def set_language_english(self):
        """Set language to English"""
        print("🌍 Changing language to English")
        return self.set_language('English')

    def set_language_polish(self):
        """Set language to Polish"""
        print("🌍 Zmiana języka na Polski")
        return self.set_language('Polski')

    def set_language_german(self):
        """Set language to German"""
        print("🌍 Sprache auf Deutsch ändern")
        return self.set_language('Deutsch')

    def set_language_russian(self):
        """Set language to Russian"""
        print("🌍 Изменение языка на Русский")
        return self.set_language('Русский')

    def toggle_time_format(self):
        """Toggle between 12h and 24h time format"""
        self.time_format = "12h" if self.time_format == "24h" else "24h"
        self.save_settings()

    def toggle_units(self):
        """Toggle between Metric and Imperial units"""
        self.units = "Imperial" if self.units == "Metric" else "Metric"
        self.save_settings()

    def update_firmware(self):
        """Simulate firmware update"""
        print("Firmware update initiated...")
        # In a real implementation, this would trigger actual firmware update

    def restart_system(self):
        """Restart the system"""
        print("System restart initiated...")
        # In a real implementation, this would restart the system

    def factory_reset(self):
        """Perform factory reset with confirmation"""
        print("Factory reset initiated...")
        # In a real implementation, this would reset all settings to defaults

    def toggle_settings_lock(self):
        """Toggle settings lock with PIN"""
        self.settings_lock_enabled = not self.settings_lock_enabled
        self.save_settings()

    def set_emergency_contact(self, contact):
        """Set emergency contact/frequency"""
        self.emergency_contact = contact
        self.save_settings()

    def toggle_secure_mode(self):
        """Toggle secure mode"""
        self.secure_mode = not self.secure_mode
        self.save_settings()

    def toggle_wifi(self):
        """Toggle WiFi connection"""
        self.wifi_enabled = not self.wifi_enabled
        self.save_settings()

    def toggle_bluetooth(self):
        """Toggle Bluetooth connection"""
        self.bluetooth_enabled = not self.bluetooth_enabled
        self.save_settings()

    def set_nmea_id(self, nmea_id):
        """Set NMEA2000/CANBus ID"""
        self.nmea_id = nmea_id
        self.save_settings()

    def calibrate_compass(self):
        """Start compass calibration"""
        print("Compass calibration started...")
        # In a real implementation, this would start compass calibration procedure

    def set_water_zero(self, value):
        """Set water sensor zero point"""
        self.water_sensor_zero = value
        self.save_settings()

    def set_fuel_zero(self, value):
        """Set fuel sensor zero point"""
        self.fuel_sensor_zero = value
        self.save_settings()

    def test_actuators(self):
        """Test system actuators"""
        print("Testing actuators...")
        # In a real implementation, this would test various actuators

    def apply_brightness(self, value):
        """Apply brightness to system"""
        try:
            if is_raspberry_pi():
                # For Raspberry Pi, control actual display brightness
                import subprocess
                brightness_value = int((value / 100) * 255)
                subprocess.run(['sudo', 'sh', '-c', f'echo {brightness_value} > /sys/class/backlight/rpi_backlight/brightness'],
                             check=False, capture_output=True)
                print(f"Applied brightness: {value}% ({brightness_value}/255)")
            else:
                print(f"Brightness set to: {value}% (PC mode - no hardware control)")
        except Exception as e:
            print(f"Error applying brightness: {e}")

    def apply_theme(self):
        """Apply theme changes to the application"""
        try:
            app = App.get_running_app()
            if hasattr(app, 'apply_theme'):
                app.apply_theme(self.theme_mode)
            print(f"Applied theme: {self.theme_mode}")
        except Exception as e:
            print(f"Error applying theme: {e}")

    def set_pin_code(self, pin=None):
        """Set PIN code for settings protection with enhanced input handling"""
        try:
            # If no PIN provided, try to get it from the input field
            if pin is None or pin == "":
                if hasattr(self, 'ids') and 'pin_input' in self.ids:
                    pin = self.ids.pin_input.text
                    print(f"🔐 Getting PIN from input field: '{pin}'")
                else:
                    print("❌ No PIN provided and no input field found")
                    self.show_message("Error", "No PIN entered. Please enter a 4-digit PIN.")
                    return False

            # Convert to string and clean
            pin = str(pin).strip()
            print(f"🔐 Processing PIN: '{pin}' (length: {len(pin)})")

            # Validate PIN
            if len(pin) >= 4 and pin.isdigit():
                self.pin_code = pin
                self.pin_enabled = True
                self.save_settings()
                print(f"✅ PIN code set successfully: {pin}")

                # Show confirmation message
                self.show_message("PIN Code Set", f"PIN code '{pin}' has been set successfully.")

                # Clear the input field
                if hasattr(self, 'ids') and 'pin_input' in self.ids:
                    self.ids.pin_input.text = ""
                    print("🧹 Cleared PIN input field")

                return True
            else:
                error_msg = f"Invalid PIN: '{pin}'. Must be at least 4 digits and contain only numbers."
                print(f"❌ {error_msg}")
                self.show_message("Invalid PIN", "PIN code must be at least 4 digits and contain only numbers.")
                return False

        except Exception as e:
            error_msg = f"Error setting PIN code: {e}"
            print(f"❌ {error_msg}")
            self.show_message("Error", f"Error setting PIN code: {e}")
            import traceback
            traceback.print_exc()
            return False

    def verify_pin(self, pin):
        """Verify PIN code"""
        if not self.pin_enabled:
            return True
        return self.pin_code == pin

    def scan_wifi_networks(self):
        """Scan for available WiFi networks with enhanced detection"""
        print("🔍 Scanning for WiFi networks...")
        try:
            if is_raspberry_pi():
                import subprocess
                networks = []

                # Method 1: Try iwlist scan
                try:
                    print("  Trying iwlist scan...")
                    result = subprocess.run(['sudo', 'iwlist', 'wlan0', 'scan'],
                                          capture_output=True, text=True, timeout=15)
                    if result.returncode == 0:
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if 'ESSID:' in line:
                                essid = line.split('ESSID:')[1].strip().strip('"')
                                if essid and essid != '' and essid != '<hidden>':
                                    networks.append(essid)
                        print(f"  iwlist found {len(networks)} networks")
                    else:
                        print(f"  iwlist failed: {result.stderr}")
                except Exception as e:
                    print(f"  iwlist error: {e}")

                # Method 2: Try nmcli if iwlist fails
                if not networks:
                    try:
                        print("  Trying nmcli scan...")
                        subprocess.run(['nmcli', 'device', 'wifi', 'rescan'],
                                     capture_output=True, timeout=10)
                        result = subprocess.run(['nmcli', '-t', '-f', 'SSID', 'device', 'wifi', 'list'],
                                              capture_output=True, text=True, timeout=10)
                        if result.returncode == 0:
                            for line in result.stdout.strip().split('\n'):
                                ssid = line.strip()
                                if ssid and ssid != '' and ssid != '--':
                                    networks.append(ssid)
                            print(f"  nmcli found {len(networks)} networks")
                    except Exception as e:
                        print(f"  nmcli error: {e}")

                # Method 3: Try iw scan as fallback
                if not networks:
                    try:
                        print("  Trying iw scan...")
                        result = subprocess.run(['sudo', 'iw', 'dev', 'wlan0', 'scan'],
                                              capture_output=True, text=True, timeout=15)
                        if result.returncode == 0:
                            lines = result.stdout.split('\n')
                            for line in lines:
                                if 'SSID:' in line:
                                    ssid = line.split('SSID:')[1].strip()
                                    if ssid and ssid != '' and ssid != '\\x00':
                                        networks.append(ssid)
                            print(f"  iw found {len(networks)} networks")
                    except Exception as e:
                        print(f"  iw error: {e}")

                # Remove duplicates and sort
                self.available_networks = sorted(list(set(networks)))

                if not self.available_networks:
                    print("  No networks found, adding test networks")
                    self.available_networks = ["TridentOS_Test", "Marina_Test", "Yacht_Test"]

            else:
                # Enhanced simulation for PC testing with more realistic names
                import random
                import time

                # Simulate scanning delay
                time.sleep(0.5)

                base_networks = [
                    "TridentOS_Network", "Marina_WiFi", "Yacht_Club", "Harbor_Guest",
                    "BoatHouse_5G", "Sailing_Club", "Port_Authority", "Marine_Radio",
                    "Dock_WiFi", "Captain_Network", "SeaView_Hotel", "Fisherman_Pub"
                ]

                # Randomly select 4-8 networks to simulate real scanning
                num_networks = random.randint(4, 8)
                self.available_networks = random.sample(base_networks, num_networks)

            print(f"✅ Found {len(self.available_networks)} networks: {self.available_networks}")

            # Force UI update
            Clock.schedule_once(lambda dt: self.update_networks_ui(), 0.1)

        except Exception as e:
            print(f"❌ Error scanning WiFi networks: {e}")
            self.available_networks = ["Error_Scanning", "Check_WiFi_Setup"]
            Clock.schedule_once(lambda dt: self.update_networks_ui(), 0.1)

    def update_networks_ui(self):
        """Update the networks list in the UI"""
        try:
            if hasattr(self, 'ids') and 'networks_list' in self.ids:
                networks_container = self.ids.networks_list
                networks_container.clear_widgets()

                if self.available_networks:
                    for network in self.available_networks:
                        btn = Button(
                            text=network,
                            font_size='16sp',
                            size_hint_y=None,
                            height=50,
                            background_normal='',
                            background_color=(0.2, 0.4, 0.6, 1),
                            color=(1, 1, 1, 1)
                        )
                        btn.bind(on_press=lambda x, net=network: self.select_network(net))

                        # Add rounded rectangle background
                        with btn.canvas.before:
                            from kivy.graphics import Color, RoundedRectangle
                            Color(0.2, 0.4, 0.6, 1)
                            btn.bg_rect = RoundedRectangle(pos=btn.pos, size=btn.size, radius=[8])
                            btn.bind(pos=lambda instance, value: setattr(instance.bg_rect, 'pos', value))
                            btn.bind(size=lambda instance, value: setattr(instance.bg_rect, 'size', value))

                        networks_container.add_widget(btn)
                else:
                    # Show "no networks" message
                    label = Label(
                        text='No networks found. Press "Scan Networks" to search.',
                        font_size='14sp',
                        size_hint_y=None,
                        height=40,
                        halign='center',
                        valign='middle',
                        color=(0.7, 0.7, 0.7, 1)
                    )
                    label.text_size = (label.width, None)
                    networks_container.add_widget(label)
        except Exception as e:
            print(f"Error updating networks UI: {e}")

    def select_network_for_connection(self, network_name):
        """Select a network for connection (shows password input if needed)"""
        try:
            print(f"📶 Selected network for connection: {network_name}")
            self.selected_network = network_name

            # For most networks, we need a password
            # In a real implementation, you might check if it's an open network
            self.wifi_password = True  # Show password input

            print(f"✅ Network '{network_name}' selected, password input shown")
            return True

        except Exception as e:
            print(f"❌ Error selecting network: {e}")
            return False

    def connect_to_wifi(self, ssid, password=""):
        """Connect to WiFi network"""
        try:
            if is_raspberry_pi():
                import subprocess
                # Create wpa_supplicant configuration
                config = f'''
network={{
    ssid="{ssid}"
    psk="{password}"
}}
'''
                with open('/tmp/wifi_config.conf', 'w') as f:
                    f.write(config)

                # Connect using wpa_supplicant
                subprocess.run(['sudo', 'wpa_supplicant', '-B', '-i', 'wlan0',
                              '-c', '/tmp/wifi_config.conf'], check=False)
                subprocess.run(['sudo', 'dhclient', 'wlan0'], check=False)

                self.connected_network = ssid
                print(f"Connected to WiFi: {ssid}")
                return True
            else:
                # Simulate connection for PC
                self.connected_network = ssid
                print(f"Simulated connection to WiFi: {ssid}")
                return True
        except Exception as e:
            print(f"Error connecting to WiFi: {e}")
            return False

    def show_virtual_keyboard(self, target_input=None, is_password=False, is_numeric=False):
        """Show virtual keyboard with enhanced debugging"""
        print(f"🔤 Showing virtual keyboard - target: {target_input}, password: {is_password}, numeric: {is_numeric}")

        try:
            # Hide existing keyboard first
            if self.current_keyboard:
                print("🔄 Hiding existing keyboard")
                self.hide_virtual_keyboard()

            if VIRTUAL_KEYBOARD_AVAILABLE:
                from widgets.virtual_keyboard import VirtualKeyboard

                print("🔧 Creating new virtual keyboard")
                self.current_keyboard = VirtualKeyboard(
                    target_input=target_input,
                    is_password=is_password,
                    is_numeric=is_numeric
                )

                # Set initial text if target has text
                if target_input and hasattr(target_input, 'text') and target_input.text:
                    self.current_keyboard.text_input = target_input.text
                    print(f"📝 Set initial text: '{target_input.text}'")

                # Add keyboard to the main layout
                if hasattr(self, 'ids') and 'keyboard_container' in self.ids:
                    print("📱 Adding keyboard to container")
                    self.ids.keyboard_container.clear_widgets()
                    self.ids.keyboard_container.add_widget(self.current_keyboard)
                    self.show_keyboard = True
                    print("✅ Virtual keyboard shown successfully")

                    # Force UI update
                    from kivy.clock import Clock
                    Clock.schedule_once(lambda dt: self.current_keyboard.update_display(), 0.1)
                else:
                    print("❌ Keyboard container not found in IDs")
                    if hasattr(self, 'ids'):
                        print(f"Available IDs: {list(self.ids.keys())}")
                    else:
                        print("No IDs attribute found")
            else:
                print("❌ Virtual keyboard not available")

        except Exception as e:
            print(f"❌ Error showing virtual keyboard: {e}")
            import traceback
            traceback.print_exc()

    def hide_virtual_keyboard(self):
        """Hide virtual keyboard"""
        try:
            if self.current_keyboard:
                if hasattr(self, 'ids') and 'keyboard_container' in self.ids:
                    self.ids.keyboard_container.clear_widgets()
                self.current_keyboard = None
                self.show_keyboard = False
                print("Virtual keyboard hidden")
        except Exception as e:
            print(f"Error hiding virtual keyboard: {e}")

    def keyboard_hidden(self):
        """Called when keyboard is hidden"""
        self.current_keyboard = None
        self.show_keyboard = False

    def show_message(self, title, message):
        """Show a message popup"""
        try:
            from kivy.uix.popup import Popup
            from kivy.uix.label import Label
            from kivy.uix.button import Button
            from kivy.uix.boxlayout import BoxLayout

            content = BoxLayout(orientation='vertical', spacing=10, padding=10)

            # Message label
            msg_label = Label(
                text=message,
                font_size='16sp',
                halign='center',
                valign='middle',
                text_size=(300, None)
            )
            content.add_widget(msg_label)

            # OK button
            ok_btn = Button(
                text='OK',
                font_size='16sp',
                size_hint_y=None,
                height=50,
                background_normal='',
                background_color=(0, 0.7, 0, 1)
            )
            content.add_widget(ok_btn)

            popup = Popup(
                title=title,
                content=content,
                size_hint=(0.6, 0.4),
                auto_dismiss=True
            )

            ok_btn.bind(on_press=popup.dismiss)
            popup.open()

        except Exception as e:
            print(f"Error showing message: {e}")

    def apply_language_changes(self):
        """Apply language changes to the interface with comprehensive UI updates"""
        try:
            print(f"🌍 Applying language changes to: {self.language}")

            if TRANSLATIONS_AVAILABLE:
                # Update all translatable text in the UI
                self.update_ui_translations()

                # Update all screens with new language
                self.update_all_screens_language()

                # Update networks list if available
                if hasattr(self, 'update_networks_ui'):
                    self.update_networks_ui()

                # Force UI refresh for all widgets
                self.force_complete_ui_refresh()

                # Update app-level language setting
                app = App.get_running_app()
                if hasattr(app, 'current_language'):
                    app.current_language = self.language

                # Show confirmation in new language
                title = get_translation("Language Changed", self.language)
                message = get_translation("Language has been changed", self.language) + f" {self.language}"
                self.show_message(title, message)

                print(f"✅ Language successfully changed to {self.language}")
            else:
                print("❌ Translation system not available")
                self.show_message("Language Changed", f"Language has been changed to {self.language}")

        except Exception as e:
            print(f"❌ Error applying language changes: {e}")
            import traceback
            traceback.print_exc()

    def update_ui_translations(self):
        """Update all UI text elements with current language"""
        try:
            if not TRANSLATIONS_AVAILABLE:
                return

            print(f"🔄 Updating UI translations for language: {self.language}")

            # Update category labels if they exist
            categories = ["DISPLAY", "LANGUAGE", "SYSTEM", "SAFETY", "CONNECTION", "CALIBRATION"]
            for category in categories:
                translated = get_translation(category, self.language)
                print(f"  {category} -> {translated}")

            # Update dynamic text elements in the settings screen
            self.update_settings_text_elements()

            # Update button texts
            self.update_button_texts()

            # Update status messages
            self.update_status_messages()

        except Exception as e:
            print(f"❌ Error updating UI translations: {e}")
            import traceback
            traceback.print_exc()

    def update_settings_text_elements(self):
        """Update specific text elements in settings screen"""
        try:
            # Update common text elements that might be visible
            text_mappings = {
                'brightness_label': 'Brightness',
                'theme_label': 'Theme Mode',
                'orientation_label': 'Screen Orientation',
                'language_label': 'Language',
                'time_format_label': 'Time Format',
                'units_label': 'Units',
                'firmware_label': 'Firmware Version',
                'pin_label': 'PIN Code Protection',
                'emergency_label': 'Emergency Contact/Frequency',
                'wifi_label': 'WiFi',
                'bluetooth_label': 'Bluetooth',
                'compass_label': 'Calibrate Compass',
                'water_sensor_label': 'Water Sensor Zero Point',
                'fuel_sensor_label': 'Fuel Sensor Zero Point'
            }

            for element_id, translation_key in text_mappings.items():
                if hasattr(self.ids, element_id):
                    translated_text = get_translation(translation_key, self.language)
                    self.ids[element_id].text = translated_text
                    print(f"  Updated {element_id}: {translated_text}")

        except Exception as e:
            print(f"❌ Error updating settings text elements: {e}")

    def update_button_texts(self):
        """Update button texts with current language"""
        try:
            # Common button texts that might need updating
            button_mappings = {
                'save_btn': 'Save',
                'cancel_btn': 'Cancel',
                'ok_btn': 'OK',
                'back_btn': 'Back',
                'scan_networks_btn': 'Scan Networks',
                'connect_btn': 'Connect',
                'set_pin_btn': 'Set PIN',
                'update_firmware_btn': 'Update Firmware',
                'restart_system_btn': 'Restart System',
                'factory_reset_btn': 'Factory Reset',
                'calibrate_compass_btn': 'Calibrate Compass',
                'test_actuators_btn': 'Test Actuators'
            }

            for button_id, translation_key in button_mappings.items():
                if hasattr(self.ids, button_id):
                    translated_text = get_translation(translation_key, self.language)
                    self.ids[button_id].text = translated_text
                    print(f"  Updated button {button_id}: {translated_text}")

        except Exception as e:
            print(f"❌ Error updating button texts: {e}")

    def update_status_messages(self):
        """Update status messages with current language"""
        try:
            # Update status-related text
            status_mappings = {
                'connected_status': 'Connected' if self.connected_network else 'Not connected',
                'wifi_status': 'ON' if self.wifi_enabled else 'OFF',
                'bluetooth_status': 'ON' if self.bluetooth_enabled else 'OFF',
                'pin_status': 'ON' if self.pin_enabled else 'OFF',
                'secure_mode_status': 'ON' if self.secure_mode else 'OFF'
            }

            for status_id, translation_key in status_mappings.items():
                if hasattr(self.ids, status_id):
                    translated_text = get_translation(translation_key, self.language)
                    self.ids[status_id].text = translated_text
                    print(f"  Updated status {status_id}: {translated_text}")

        except Exception as e:
            print(f"❌ Error updating status messages: {e}")

    def update_all_screens_language(self):
        """Update language for all screens in the application"""
        try:
            print(f"🔄 Updating language for all screens to: {self.language}")

            if hasattr(self, 'manager') and self.manager:
                for screen_name in self.manager.screen_names:
                    screen = self.manager.get_screen(screen_name)
                    if hasattr(screen, 'update_language'):
                        screen.update_language(self.language)
                        print(f"  Updated language for screen: {screen_name}")

        except Exception as e:
            print(f"❌ Error updating all screens language: {e}")

    def force_complete_ui_refresh(self):
        """Force complete UI refresh for language changes"""
        try:
            print(f"🔄 Forcing complete UI refresh for language: {self.language}")

            # Force property updates
            for prop_name in ['language', 'selected_category']:
                if hasattr(self, prop_name):
                    prop = self.property(prop_name)
                    if prop:
                        prop.dispatch(self)

            # Force canvas update for this screen
            if hasattr(self, 'canvas'):
                self.canvas.ask_update()

            # Update all child widgets
            if hasattr(self, 'walk'):
                for widget in self.walk():
                    if hasattr(widget, 'canvas'):
                        widget.canvas.ask_update()
                    # Update text properties if they exist
                    if hasattr(widget, 'text') and hasattr(widget, 'translation_key'):
                        widget.text = get_translation(widget.translation_key, self.language)

            # Force manager refresh if available
            if hasattr(self, 'manager') and self.manager:
                if hasattr(self.manager, 'canvas'):
                    self.manager.canvas.ask_update()

            print("✅ Complete UI refresh completed")

        except Exception as e:
            print(f"❌ Error forcing complete UI refresh: {e}")

    def force_ui_refresh(self):
        """Force complete UI refresh to apply language changes"""
        try:
            print(f"🔄 Forcing UI refresh for language: {self.language}")

            # Force canvas update
            if hasattr(self, 'canvas'):
                self.canvas.ask_update()

            # Update all child widgets
            if hasattr(self, 'walk'):
                for widget in self.walk():
                    if hasattr(widget, 'canvas'):
                        widget.canvas.ask_update()

            # Trigger property updates
            for prop_name in ['language', 'selected_category']:
                if hasattr(self, prop_name):
                    prop = self.property(prop_name)
                    if prop:
                        prop.dispatch(self)

            print("✅ UI refresh completed")

        except Exception as e:
            print(f"❌ Error forcing UI refresh: {e}")

    def select_network(self, network_name):
        """Select a WiFi network for connection"""
        self.selected_network = network_name
        print(f"Selected network: {network_name}")
        # Show password input if needed (for now, always show for security)
        self.wifi_password = "show"

    def go_to_home(self):
        """Return to home screen"""
        self.manager.current = 'home'


class AlarmScreen(TranslatableScreen):
    update_event = None

    def setup_translations(self):
        """Setup translatable elements for AlarmScreen"""
        print("🌍 Setting up translations for AlarmScreen")
        # Register translatable elements if any exist in the UI
        print("✅ AlarmScreen translations setup completed")

    def on_enter(self):
        print("=== WEJŚCIE NA EKRAN ALARMÓW ===")
        # Aktualizacja danych przy wejściu na ekran
        self.update_from_state(None)

        # Uruchomienie aktualizacji co 1 sekundę
        self.update_event = Clock.schedule_interval(self.update_from_state, 1)
        print("=== ZAKOŃCZENIE WEJŚCIA NA EKRAN ALARMÓW ===")

    def on_leave(self):
        # Zatrzymanie aktualizacji przy wyjściu z ekranu
        if self.update_event:
            self.update_event.cancel()
            self.update_event = None

    def update_from_state(self, dt=None):
        print("=== ROZPOCZĘCIE AKTUALIZACJI UI W ALARMSCREEN ===")
        try:
            # Pobieranie danych z aplikacji
            app = App.get_running_app()

            # Debugowanie alarmów
            self.debug_alarms()

            # Aktualizacja statusu systemu
            if hasattr(self.ids, 'system_status'):
                self.ids.system_status.text = "OK" if not app.has_active_alarms else "ALERT"
                self.ids.system_status.color = (0, 1, 0, 1) if not app.has_active_alarms else (1, 0, 0, 1)

            # Aktualizacja listy alarmów
            self.update_alarm_list()

            # Wymuszenie odświeżenia widoku
            self.canvas.ask_update()
            print("=== ZAKOŃCZENIE AKTUALIZACJI UI W ALARMSCREEN ===")
        except Exception as e:
            print(f"Błąd aktualizacji UI w AlarmScreen: {e}")
            import traceback
            traceback.print_exc()

    def debug_alarms(self):
        """Wyświetla informacje debugowania o alarmach"""
        app = App.get_running_app()

        # Sprawdzenie, czy menedżer alarmów jest zainicjalizowany
        if not app.alarm_manager_instance:
            print("UWAGA: Menedżer alarmów nie jest zainicjalizowany!")
            return

        # Pobranie alarmów
        active_alarms = app.get_active_alarms()

        # Wyświetlenie informacji o alarmach
        print(f"=== INFORMACJE O ALARMACH ===")
        print(f"Liczba aktywnych alarmów: {len(active_alarms)}")
        print(f"Flaga has_active_alarms: {app.has_active_alarms}")
        print(f"Flaga has_critical_alarms: {app.has_critical_alarms}")

        # Sprawdzenie zawartości pliku system_state.json
        try:
            import json
            import os
            state_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "system_state.json")
            if os.path.exists(state_file):
                with open(state_file, "r") as f:
                    state_data = json.load(f)
                    print(f"Zawartość system_state.json:")
                    if 'alarms' in state_data:
                        print(f"  Sekcja 'alarms' istnieje")
                        print(f"  Aktywne alarmy w pliku: {len(state_data['alarms'].get('active', []))}")
                        for alarm in state_data['alarms'].get('active', []):
                            print(f"    - {alarm.get('message', 'Brak wiadomości')} ({alarm.get('type', 'Brak typu')})")
                    else:
                        print(f"  Sekcja 'alarms' NIE istnieje w pliku!")
            else:
                print(f"Plik system_state.json nie istnieje!")
        except Exception as e:
            print(f"Błąd podczas sprawdzania pliku system_state.json: {e}")

        # Wyświetlenie szczegółów aktywnych alarmów
        if active_alarms:
            print("Aktywne alarmy:")
            for i, alarm in enumerate(active_alarms, 1):
                print(f"  {i}. ID: {alarm['id']}")
                print(f"     Typ: {alarm['type']}")
                print(f"     Źródło: {alarm['source']}")
                print(f"     Wiadomość: {alarm['message']}")
                print(f"     Czas: {alarm['timestamp']}")
                print(f"     Potwierdzony: {alarm.get('acknowledged', False)}")
        else:
            print("Brak aktywnych alarmów.")

    def update_alarm_list(self):
        """Aktualizuje listę alarmów - wyświetla wszystkie typy alarmów"""
        try:
            print("AlarmScreen.update_alarm_list: Rozpoczęcie aktualizacji listy alarmów")
            app = App.get_running_app()
            alarm_list = self.ids.alarm_list

            # Wyczyszczenie listy
            alarm_list.clear_widgets()

            # Wymuszenie wczytania alarmów z pliku
            if app.alarm_manager_instance:
                # Najpierw wykonaj wymuszenie zapisania aktualnego stanu alarmów do pliku
                app.save_state_to_file()

                # Wykonaj czyszczenie alarmów przed wyświetleniem
                print("AlarmScreen.update_alarm_list: Wykonywanie czyszczenia alarmów przed wyświetleniem")
                removed_count = app.alarm_manager_instance.clean_up_alarms()
                print(f"AlarmScreen.update_alarm_list: Usunięto {removed_count} alarmów podczas czyszczenia")

                # Aktualizacja właściwości alarmowych po czyszczeniu
                app.update_alarm_properties()

            # Pobranie wszystkich alarmów z menedżera alarmów (bez filtrowania)
            alarms = app.get_active_alarms()

            print(f"AlarmScreen.update_alarm_list: Znaleziono {len(alarms)} alarmów")

            # Sprawdzenie, czy wszystkie alarmy są faktycznie aktywne
            inactive_alarms = [alarm for alarm in alarms if not alarm.get('active', True)]
            if inactive_alarms:
                print(f"AlarmScreen.update_alarm_list: Znaleziono {len(inactive_alarms)} nieaktywnych alarmów, które zostaną usunięte")
                for alarm in inactive_alarms:
                    print(f"AlarmScreen.update_alarm_list: Usuwanie nieaktywnego alarmu: {alarm['id']}")
                    app.deactivate_alarm(alarm['id'])

                # Ponowne pobranie alarmów po usunięciu
                alarms = app.get_active_alarms()
                print(f"AlarmScreen.update_alarm_list: Po usunięciu nieaktywnych alarmów pozostało {len(alarms)} alarmów")

            # Sprawdzenie warunków systemowych, które mogą wymagać dezaktywacji alarmów
            state_data = state_manager.load_state()

            # Sprawdzenie poziomu baterii
            if 'battery_level' in state_data and state_data['battery_level'] >= 20:
                battery_alarms = [alarm for alarm in alarms
                                 if alarm['source'] == alarm_manager.SOURCE_BATTERY
                                 and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                if battery_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(battery_alarms)} alarmów baterii do usunięcia (poziom baterii: {state_data['battery_level']}%)")
                    for alarm in battery_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu baterii: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów baterii pozostało {len(alarms)} alarmów")

            # Sprawdzenie temperatury silnika
            if 'engine_temperature' in state_data and state_data['engine_temperature'] <= 110:
                engine_temp_alarms = [alarm for alarm in alarms
                                     if alarm['source'] == alarm_manager.SOURCE_ENGINE
                                     and "temperature" in alarm['message'].lower()]

                if engine_temp_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(engine_temp_alarms)} alarmów temperatury silnika do usunięcia (temperatura: {state_data['engine_temperature']}°C)")
                    for alarm in engine_temp_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu temperatury silnika: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów temperatury silnika pozostało {len(alarms)} alarmów")

            # Sprawdzenie poziomu wody
            if 'water_level' in state_data and state_data['water_level'] >= 10:
                water_alarms = [alarm for alarm in alarms
                               if alarm['source'] == alarm_manager.SOURCE_WATER
                               and "low water" in alarm['message'].lower()]

                if water_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(water_alarms)} alarmów poziomu wody do usunięcia (poziom wody: {state_data['water_level']}%)")
                    for alarm in water_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu poziomu wody: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów poziomu wody pozostało {len(alarms)} alarmów")

            # Sprawdzenie poziomu paliwa
            if 'fuel_level' in state_data and state_data['fuel_level'] >= 15:
                fuel_alarms = [alarm for alarm in alarms
                              if alarm['source'] == alarm_manager.SOURCE_FUEL
                              and "low fuel" in alarm['message'].lower()]

                if fuel_alarms:
                    print(f"AlarmScreen.update_alarm_list: Znaleziono {len(fuel_alarms)} alarmów poziomu paliwa do usunięcia (poziom paliwa: {state_data['fuel_level']}%)")
                    for alarm in fuel_alarms:
                        print(f"AlarmScreen.update_alarm_list: Usuwanie alarmu poziomu paliwa: {alarm['id']}")
                        app.deactivate_alarm(alarm['id'])

                    # Ponowne pobranie alarmów po usunięciu
                    alarms = app.get_active_alarms()
                    print(f"AlarmScreen.update_alarm_list: Po usunięciu alarmów poziomu paliwa pozostało {len(alarms)} alarmów")

            # Dodanie widgetów alarmów do listy
            for alarm in alarms:
                # Sprawdzenie, czy alarm jest aktywny
                if not alarm.get('active', True):
                    print(f"AlarmScreen.update_alarm_list: Pomijanie nieaktywnego alarmu: {alarm['id']}")
                    continue

                # Tworzenie widgetu alarmu z wszystkimi potrzebnymi informacjami
                alarm_item = AlarmItem(
                    alarm_id=alarm['id'],
                    alarm_type=alarm['type'],
                    alarm_message=alarm['message'],
                    alarm_timestamp=alarm['timestamp'],
                    alarm_source=alarm['source'],
                    alarm_acknowledged=alarm.get('acknowledged', False),
                    alarm_active=alarm.get('active', True)
                )
                alarm_list.add_widget(alarm_item)

                # Dodanie separatora między alarmami
                if alarms.index(alarm) < len(alarms) - 1:
                    separator = Widget(
                        size_hint_y=None,
                        height=10
                    )
                    alarm_list.add_widget(separator)

                print(f"AlarmScreen.update_alarm_list: Dodano alarm: {alarm['message']} ({alarm['type']}), aktywny: {alarm.get('active', True)}, potwierdzony: {alarm.get('acknowledged', False)}")

            # Jeśli nie ma alarmów, dodaj informację
            if not alarms or all(not alarm.get('active', True) for alarm in alarms):
                label = Label(
                    text="No active alarms",
                    font_size=18,
                    size_hint_y=None,
                    height=50
                )
                alarm_list.add_widget(label)
                print("AlarmScreen.update_alarm_list: Dodano informację o braku alarmów")

            # Wymuszenie zapisania stanu po wszystkich zmianach
            app.save_state_to_file()

            print("AlarmScreen.update_alarm_list: Zakończenie aktualizacji listy alarmów")

        except Exception as e:
            print(f"AlarmScreen.update_alarm_list: Błąd aktualizacji listy alarmów: {e}")
            import traceback
            traceback.print_exc()

    def acknowledge_all_alarms(self):
        """Potwierdza wszystkie aktywne alarmy"""
        app = App.get_running_app()
        app.acknowledge_all_alarms()
        self.update_alarm_list()

    def deactivate_all_alarms(self):
        """Dezaktywuje wszystkie alarmy"""
        app = App.get_running_app()
        app.deactivate_all_alarms()
        self.update_alarm_list()

    def add_test_alarm(self):
        """Dodaje testowy alarm"""
        app = App.get_running_app()

        # Dodanie testowego alarmu
        app.add_alarm(
            alarm_manager.ALARM_CRITICAL,
            "This is a critical test alarm. Please acknowledge.",
            alarm_manager.SOURCE_SYSTEM
        )

        # Aktualizacja listy alarmów
        self.update_alarm_list()

        # Wyświetlenie informacji
        print("Dodano testowy alarm krytyczny.")

    def go_to_home(self):
        self.manager.current = 'home'

class MainApp(App, TranslatableApp):
    # Właściwości do bindowania w UI
    # Dane klimatyzacji
    climate_temp = NumericProperty(22)
    fridge_temp = NumericProperty(4)
    auto_ac = BooleanProperty(True)

    # Current language setting
    current_language = StringProperty("English")

    def __init__(self, **kwargs):
        App.__init__(self, **kwargs)
        TranslatableApp.__init__(self)

    def on_custom_app_language_change(self, new_language):
        """Custom handler for app-level language changes"""
        print(f"🌍 MainApp handling language change to: {new_language}")

        # Update current language property
        self.current_language = new_language

        # Update settings screen language if available
        try:
            if hasattr(self, 'root') and self.root:
                settings_screen = self.root.get_screen('settings')
                if hasattr(settings_screen, 'language'):
                    settings_screen.language = new_language
                    print(f"✅ Updated settings screen language to: {new_language}")
        except Exception as e:
            print(f"⚠️ Could not update settings screen language: {e}")

        # Force UI refresh for all screens
        self.force_ui_language_refresh()

    def force_ui_language_refresh(self):
        """Force refresh of UI elements that use app.get_translation()"""
        print(f"🔄 Forcing UI language refresh for all screens")

        try:
            if hasattr(self, 'root') and self.root:
                # Update all screens with new translations
                for screen_name in self.root.screen_names:
                    try:
                        screen = self.root.get_screen(screen_name)
                        self.update_screen_translations(screen, screen_name)
                        print(f"  ✅ Updated translations for screen: {screen_name}")
                    except Exception as e:
                        print(f"  ⚠️ Could not update screen {screen_name}: {e}")

                print(f"✅ UI language refresh completed")
        except Exception as e:
            print(f"❌ Error during UI language refresh: {e}")
            import traceback
            traceback.print_exc()

    def update_screen_translations(self, screen, screen_name):
        """Update translations for a specific screen"""
        if not hasattr(screen, 'ids'):
            return

        # Define translation mappings for each screen
        translation_mappings = {
            'home': {
                'home_title_label': 'HOME',
                'lightning_label': 'LIGHTNING',
                'alarm_label': 'ALARM',
                'climate_label': 'CLIMATE',
                'battery_label': 'BATTERY',
                'engine_label': 'ENGINE',
                'water_label': 'WATER',
                'autopilot_label': 'AUTOPILOT',
                'fuel_label': 'FUEL'
            },
            'lightning': {
                'navigation_label': 'NAVIGATION LIGHTNING',
                'interior_label': 'INTERIOR LIGHTNING',
                'deck_label': 'DECK LIGHTNING',
                'system_status_label': 'SYSTEM STATUS',
                'red_label': 'Red',
                'green_label': 'Green',
                'blue_label': 'Blue',
                'intensity_label': 'Intensity',
                'go_home_btn': 'Go to Home'
            },
            'engine': {
                'engine_title': 'ENGINE CONTROL',
                'engine_status_title': 'ENGINE STATUS',
                'throttle_title': 'THROTTLE CONTROL',
                'engine_params_title': 'ENGINE PARAMETERS'
            }
        }

        # Get mappings for this screen
        mappings = translation_mappings.get(screen_name, {})

        # Update each widget
        for widget_id, translation_key in mappings.items():
            try:
                widget = screen.ids.get(widget_id)
                if widget and hasattr(widget, 'text'):
                    old_text = widget.text
                    new_text = self.get_translation(translation_key)
                    widget.text = new_text
                    print(f"    📝 {widget_id}: {old_text} → {new_text}")
            except Exception as e:
                print(f"    ❌ Error updating {widget_id}: {e}")

    def get_translation(self, key, language=None):
        """Get translation for UI elements with enhanced language detection"""
        try:
            if TRANSLATIONS_AVAILABLE:
                # Get current language from multiple sources
                if language is None:
                    # Use app-level language first
                    language = self.current_language if hasattr(self, 'current_language') else "English"

                return get_translation(key, language)
            else:
                return key
        except Exception as e:
            print(f"❌ Error getting translation: {e}")
            return key

    def update_app_language(self, language):
        """Update application-wide language setting"""
        try:
            print(f"🌍 Updating app-wide language to: {language}")

            if TRANSLATIONS_AVAILABLE:
                from translations import validate_language

                if validate_language(language):
                    # Update app-level language property
                    old_language = self.current_language
                    self.current_language = language

                    # Force property update to trigger UI refresh
                    self.property('current_language').dispatch(self)

                    print(f"✅ App current_language updated: {old_language} → {language}")

                    # Update all screens if they have update_language method
                    if hasattr(self, 'root') and self.root:
                        for screen_name in self.root.screen_names:
                            screen = self.root.get_screen(screen_name)
                            if hasattr(screen, 'update_language'):
                                print(f"🔄 Updating language for screen: {screen_name}")
                                screen.update_language(language)

                            # Force property updates on screen level
                            if hasattr(screen, 'language'):
                                try:
                                    screen.language = language
                                    screen.property('language').dispatch(screen)
                                except:
                                    pass

                    print(f"✅ App-wide language updated to: {language}")
                    return True
                else:
                    print(f"❌ Invalid language: {language}")
                    return False
            else:
                # Fallback for when translation system is not available
                old_language = self.current_language
                self.current_language = language
                self.property('current_language').dispatch(self)
                print(f"⚠️ Translation system not available - updated current_language: {old_language} → {language}")
                return True

        except Exception as e:
            print(f"❌ Error updating app language: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_current_language(self):
        """Get current application language"""
        try:
            # Try to get from settings screen first
            if hasattr(self, 'root') and self.root:
                try:
                    settings_screen = self.root.get_screen('settings')
                    return settings_screen.language
                except:
                    pass

            # Fallback to app-level language
            return self.current_language if hasattr(self, 'current_language') else "English"

        except Exception as e:
            print(f"❌ Error getting current language: {e}")
            return "English"

    def force_language_ui_refresh(self):
        """Force complete UI refresh after language change"""
        try:
            print(f"🔄 Forcing language UI refresh...")

            if hasattr(self, 'root') and self.root:
                # Force property update on app level
                self.property('current_language').dispatch(self)

                # Refresh all screens
                for screen_name in self.root.screen_names:
                    screen = self.root.get_screen(screen_name)

                    # Force canvas redraw
                    if hasattr(screen, 'canvas'):
                        screen.canvas.ask_update()

                    # Update screen if it has language update method
                    if hasattr(screen, 'update_language'):
                        screen.update_language(self.current_language)

                    # Force property updates on screen
                    if hasattr(screen, 'property'):
                        for prop_name in ['language', 'current_language']:
                            if hasattr(screen, prop_name):
                                try:
                                    screen.property(prop_name).dispatch(screen)
                                except:
                                    pass

                # Force complete canvas update
                if hasattr(self.root, 'canvas'):
                    self.root.canvas.ask_update()

                print(f"✅ Language UI refresh completed")

        except Exception as e:
            print(f"❌ Error forcing language UI refresh: {e}")
            import traceback
            traceback.print_exc()
    external_temp = NumericProperty(14)
    fan_power = NumericProperty(50)
    ac_mode = StringProperty("off")
    current_internal_temp = NumericProperty(22)

    # Dane oświetlenia
    interior_light_active = BooleanProperty(False)
    navigation_light_active = BooleanProperty(False)
    deck_light_active = BooleanProperty(False)
    interior_r = NumericProperty(255)
    interior_g = NumericProperty(255)
    interior_b = NumericProperty(255)
    interior_intensity = NumericProperty(100)

    # Dane baterii
    battery_level = NumericProperty(80)
    power_consumption = NumericProperty(15)
    charging = BooleanProperty(False)
    power_source = StringProperty("Battery")
    time_remaining = StringProperty("4h 20m")

    # Parametry symulacji baterii
    sim_discharge_rate = NumericProperty(0.5)
    sim_charge_rate = NumericProperty(1.0)

    # Dane zbiorników
    water_level = NumericProperty(50)
    fuel_level = NumericProperty(60)

    # Dane silnika i autopilota
    engine_status = StringProperty("active")
    engine_throttle = NumericProperty(0)
    engine_temperature = NumericProperty(75)
    engine_hours = NumericProperty(0)
    autopilot = BooleanProperty(False)
    emergency = BooleanProperty(False)

    # Dane alarmów
    has_active_alarms = BooleanProperty(False)
    has_critical_alarms = BooleanProperty(False)
    alarm_status = StringProperty("OK")

    # Aktualny czas
    current_time = StringProperty("")

    # Menedżer alarmów
    alarm_manager_instance = None

    # Pamięć podręczna stanu dla optymalizacji
    _state_cache = {}
    _last_state_update = 0
    _last_alarm_update = 0
    _state_update_interval = 5.0  # Interwał aktualizacji stanu w sekundach
    _alarm_update_interval = 2.0  # Interwał aktualizacji alarmów w sekundach

    def clean_all_alarms(self):
        """Czyści wszystkie nieaktywne alarmy i alarmy, które nie powinny być aktywne"""
        print("=== ROZPOCZĘCIE CZYSZCZENIA WSZYSTKICH ALARMÓW ===")
        if not self.alarm_manager_instance:
            print("Menedżer alarmów nie jest zainicjalizowany!")
            return 0

        # Najpierw wykonaj ogólne czyszczenie alarmów
        removed_count = self.alarm_manager_instance.clean_up_alarms()
        print(f"Usunięto {removed_count} alarmów podczas czyszczenia")

        # Wczytanie aktualnego stanu (używane przez alarm_manager.get_active_alarms())
        state_manager.load_state()

        # Pobranie wszystkich aktywnych alarmów
        active_alarms = self.alarm_manager_instance.get_active_alarms()
        alarms_to_remove = []

        # Sprawdzenie warunków systemowych, które mogą wymagać dezaktywacji alarmów
        # Sprawdzenie poziomu baterii
        if self.battery_level >= 20:
            print(f"Poziom baterii ({int(self.battery_level)}%) jest powyżej progu krytycznego (20%)")
            print("Usuwanie wszystkich alarmów baterii...")

            battery_alarms = [alarm for alarm in active_alarms
                             if alarm['source'] == alarm_manager.SOURCE_BATTERY
                             and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

            if battery_alarms:
                print(f"Znaleziono {len(battery_alarms)} alarmów baterii do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in battery_alarms])
            else:
                print("Brak alarmów baterii do usunięcia")

        # Sprawdzenie temperatury silnika
        if hasattr(self, 'engine_temperature') and self.engine_temperature <= 110:
            print(f"Temperatura silnika ({self.engine_temperature:.1f}°C) jest poniżej progu krytycznego (110°C)")
            print("Usuwanie wszystkich alarmów temperatury silnika...")

            engine_temp_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == alarm_manager.SOURCE_ENGINE
                                 and "temperature" in alarm['message'].lower()]

            if engine_temp_alarms:
                print(f"Znaleziono {len(engine_temp_alarms)} alarmów temperatury silnika do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in engine_temp_alarms])
            else:
                print("Brak alarmów temperatury silnika do usunięcia")

        # Sprawdzenie poziomu wody
        if self.water_level >= 10:
            print(f"Poziom wody ({int(self.water_level)}%) jest powyżej progu krytycznego (10%)")
            print("Usuwanie wszystkich alarmów poziomu wody...")

            water_alarms = [alarm for alarm in active_alarms
                           if alarm['source'] == alarm_manager.SOURCE_WATER
                           and "low water" in alarm['message'].lower()]

            if water_alarms:
                print(f"Znaleziono {len(water_alarms)} alarmów poziomu wody do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in water_alarms])
            else:
                print("Brak alarmów poziomu wody do usunięcia")

        # Sprawdzenie poziomu paliwa
        if self.fuel_level >= 15:
            print(f"Poziom paliwa ({int(self.fuel_level)}%) jest powyżej progu krytycznego (15%)")
            print("Usuwanie wszystkich alarmów poziomu paliwa...")

            fuel_alarms = [alarm for alarm in active_alarms
                          if alarm['source'] == alarm_manager.SOURCE_FUEL
                          and "low fuel" in alarm['message'].lower()]

            if fuel_alarms:
                print(f"Znaleziono {len(fuel_alarms)} alarmów poziomu paliwa do usunięcia")
                alarms_to_remove.extend([alarm['id'] for alarm in fuel_alarms])
            else:
                print("Brak alarmów poziomu paliwa do usunięcia")

        # Usunięcie wszystkich alarmów, które powinny być nieaktywne
        for alarm_id in alarms_to_remove:
            print(f"Usuwanie alarmu: {alarm_id}")
            self.alarm_manager_instance.deactivate_alarm(alarm_id)

        # Wymuszenie zapisania zmian
        self.alarm_manager_instance.save_alarms()
        print(f"Usunięto łącznie {len(alarms_to_remove)} alarmów")

        # Aktualizacja właściwości alarmowych
        self.update_alarm_properties()

        print("=== ZAKOŃCZENIE CZYSZCZENIA WSZYSTKICH ALARMÓW ===")
        return len(alarms_to_remove)

    def build(self):
        print("🚀 *** BUILD FUNCTION CALLED ***")
        # Flaga do blokowania aktualizacji UI podczas programowych zmian wartości
        self.updating_ui = False

        # Inicjalizacja menedżera alarmów
        self.alarm_manager_instance = alarm_manager.get_instance()

        # Inicjalizacja kontrolera buzzera
        self.buzzer_controller = get_buzzer_controller() if BUZZER_AVAILABLE else None
        if self.buzzer_controller:
            print("Buzzer controller initialized successfully")
        else:
            print("Buzzer controller not available")

        # NATYCHMIASTOWA konfiguracja fullscreen - BEZ OPÓŹNIENIA
        Clock.schedule_once(self.setup_fullscreen, 0)

        # Wczytanie początkowego stanu
        self.update_state_from_file(None)

        # Jednorazowe czyszczenie wszystkich alarmów przy starcie aplikacji
        removed_count = self.clean_all_alarms()

        # Uruchomienie monitora alarmów
        self.alarm_monitor = alarm_monitor.start_monitoring()

        # Uruchomienie cyklicznego wczytywania stanu z interwałem dostosowanym do platformy
        self.state_update_event = Clock.schedule_interval(self.update_state_from_file, _state_update_interval)

        # Uruchomienie cyklicznego czyszczenia alarmów z interwałem dostosowanym do platformy
        self.alarm_cleanup_event = Clock.schedule_interval(self.periodic_alarm_cleanup, _alarm_check_interval * 15)

        # Uruchomienie okresowego czyszczenia pamięci z interwałem dostosowanym do platformy
        self.gc_event = Clock.schedule_interval(self.perform_gc, _memory_cleanup_interval)

        # Uruchomienie symulacji klimatyzacji z interwałem dostosowanym do platformy
        self.climate_simulation_event = Clock.schedule_interval(self.simulate_climate_system, _state_update_interval)

        # Utworzenie zoptymalizowanego menedżera ekranów
        if OPTIMIZATIONS_AVAILABLE:
            sm = create_optimized_screen_manager()
        else:
            sm = ScreenManager(transition=NoTransition())
        sm.app = self
        sm.add_widget(HomeScreen(name="home"))
        sm.add_widget(ClimateScreen(name="climate"))
        sm.add_widget(LightningScreen(name="lightning"))
        sm.add_widget(BatteryScreen(name="battery"))
        sm.add_widget(AlarmScreen(name="alarm"))
        sm.add_widget(EngineScreen(name="engine"))
        sm.add_widget(WaterScreen(name="water"))
        sm.add_widget(FuelScreen(name="fuel"))
        sm.add_widget(AutopilotScreen(name="autopilot"))
        sm.add_widget(SettingsScreen(name="settings"))

        # Force start on HOME screen
        sm.current = 'home'
        print("🏠 *** FORCED START ON HOME SCREEN ***")

        return sm

    def setup_fullscreen(self, dt):
        """
        WYMUSZA fullscreen natychmiast po uruchomieniu aplikacji.
        Fullscreen jest włączony na WSZYSTKICH platformach od razu.
        """
        try:
            from kivy.core.window import Window

            print("=== WYMUSZANIE FULLSCREEN ===")

            # WYMUŚ FULLSCREEN NA WSZYSTKICH PLATFORMACH
            Window.fullscreen = 'auto'
            Window.borderless = True
            Window.show_cursor = True
            Window.clearcolor = (0, 0, 0, 1)  # Czarne tło

            if is_raspberry_pi():
                print("Raspberry Pi 5 - FULLSCREEN WYMUSZONY")
                Window.set_title('TridentOS')

                # Dodatkowe wymuszenie dla RPi5
                Window.maximize()

            else:
                print("PC - FULLSCREEN WYMUSZONY (ESC aby wyjść)")
                Window.set_title('TridentOS - Marine Control System')

                # Dodaj obsługę ESC dla wyjścia z fullscreen na PC
                Window.bind(on_key_down=self.on_key_down)

            # Dodatkowe wymuszenie fullscreen
            Clock.schedule_once(self.force_fullscreen_again, 0.5)
            Clock.schedule_once(self.force_fullscreen_again, 1.0)
            Clock.schedule_once(self.force_fullscreen_again, 2.0)

            print(f"FULLSCREEN STATUS: {Window.fullscreen}")
            print(f"BORDERLESS: {Window.borderless}")
            print(f"WINDOW SIZE: {Window.size}")

        except Exception as e:
            print(f"Błąd podczas wymuszania fullscreen: {e}")

    def force_fullscreen_again(self, dt):
        """Dodatkowe wymuszenie fullscreen po czasie"""
        try:
            from kivy.core.window import Window
            if not Window.fullscreen:
                print("PONOWNE WYMUSZANIE FULLSCREEN...")
                Window.fullscreen = 'auto'
                Window.borderless = True
        except Exception as e:
            print(f"Błąd ponownego wymuszania fullscreen: {e}")

    def on_key_down(self, window, key, scancode, codepoint, modifier):
        """
        Obsługuje naciśnięcia klawiszy.
        ESC - wychodzi z fullscreen (tylko na PC)
        F11 - przywraca fullscreen (tylko na PC)
        """
        try:
            from kivy.core.window import Window

            # ESC - wyjdź z fullscreen (tylko na PC)
            if key == 27 and not is_raspberry_pi():  # ESC
                if Window.fullscreen:
                    Window.fullscreen = False
                    Window.borderless = False
                    Window.size = (1280, 720)  # Przywróć rozmiar okna
                    print("FULLSCREEN WYŁĄCZONY (ESC) - Naciśnij F11 aby przywrócić")
                return True

            # F11 - przywróć fullscreen (tylko na PC)
            elif key == 292 and not is_raspberry_pi():  # F11
                Window.fullscreen = 'auto'
                Window.borderless = True
                print("FULLSCREEN PRZYWRÓCONY (F11)")
                return True

        except Exception as e:
            print(f"Błąd obsługi klawisza: {e}")

        return False

    def perform_gc(self, dt):
        """
        Wykonuje czyszczenie pamięci i optymalizację zasobów - zoptymalizowane dla RPi5.
        """
        current_time = time.time()
        if current_time - _performance_stats['last_gc_time'] >= _performance_stats['gc_interval']:
            # Użyj zoptymalizowanej funkcji czyszczenia pamięci
            if OPTIMIZATIONS_AVAILABLE:
                cleanup_memory()
                collected = 0  # cleanup_memory() już wywołuje gc.collect()
            else:
                # Fallback - standardowe czyszczenie
                collected = gc.collect()
                Cache.remove('kv.image')
                Cache.remove('kv.texture')

                if is_raspberry_pi():
                    Cache.remove('kv.shader')
                    if hasattr(gc, 'set_threshold'):
                        gc.set_threshold(700, 10, 10)

            # Aktualizacja statystyk
            _performance_stats['last_gc_time'] = current_time
            _performance_stats['memory_cleanups'] += 1

            # Logowanie tylko na RPi5 dla diagnostyki
            if is_raspberry_pi() and collected > 0:
                print(f"GC: Zwolniono {collected} obiektów, cleanup #{_performance_stats['memory_cleanups']}")

    def save_state_to_file(self):
        """
        Zapisuje stan do pliku system_state.json.
        """
        try:
            print("=== ROZPOCZĘCIE ZAPISU STANU DO PLIKU ===")

            # Wczytanie aktualnego stanu, aby zachować sekcję 'alarms'
            current_state = state_manager.load_state()

            # Tworzenie słownika z danymi stanu
            state_data = {}
            for key in dir(self):
                if key.startswith('__') or key in ['root', 'updating_ui', 'state_update_event', 'time_update_event', 'alarm_manager_instance']:
                    continue

                value = getattr(self, key)
                if isinstance(value, (int, float, bool, str)) or value is None:
                    state_data[key] = value

            # Zachowanie sekcji 'alarms' z aktualnego stanu
            if 'alarms' in current_state:
                state_data['alarms'] = current_state['alarms']
            elif self.alarm_manager_instance:
                # Jeśli nie ma sekcji 'alarms' w pliku, ale menedżer alarmów jest zainicjalizowany,
                # pobierz alarmy bezpośrednio z menedżera alarmów
                print("Brak sekcji 'alarms' w pliku system_state.json - pobieranie alarmów z menedżera alarmów")
                alarms_data = {
                    'active': self.alarm_manager_instance.get_active_alarms(),
                    'history': self.alarm_manager_instance.get_alarm_history(),
                    'last_update': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                state_data['alarms'] = alarms_data
                print(f"Dodano sekcję 'alarms' do pliku system_state.json - aktywne alarmy: {len(alarms_data['active'])}")

            # Dodanie flag statusu alarmów
            if self.alarm_manager_instance:
                state_data['has_active_alarms'] = self.alarm_manager_instance.has_active_alarms()
                state_data['has_critical_alarms'] = self.alarm_manager_instance.has_critical_alarms()

            # Zapisanie stanu do pliku
            state_manager.save_state(state_data)

            print("=== ZAKOŃCZENIE ZAPISU STANU DO PLIKU ===")
        except Exception as e:
            print(f"Błąd zapisu stanu do pliku: {e}")
            import traceback
            traceback.print_exc()

    # Metoda update_time została usunięta, ponieważ ClockWidget jest teraz autonomiczny

    def update_state_from_file(self, dt=None):
        """
        Wczytuje stan z pliku system_state.json.
        Ta metoda jest wywoływana regularnie przez zegar.
        Zoptymalizowana o wykrywanie zmian i selektywną aktualizację UI.
        """
        # Sprawdzenie, czy minął wymagany interwał
        current_time = time.time()
        if dt is not None and current_time - self._last_state_update < self._state_update_interval:
            return  # Zbyt wcześnie na aktualizację

        # Ustawienie flagi, aby uniknąć pętli zwrotnej
        self.updating_ui = True

        try:
            # Sprawdzenie, czy stan się zmienił
            state_data = state_manager.load_state()

            # Jeśli stan się nie zmienił, pomijamy aktualizację
            if state_data == self._state_cache and dt is not None:
                self.updating_ui = False
                return

            # Zapisanie nowego stanu w pamięci podręcznej
            self._state_cache = state_data.copy()
            self._last_state_update = current_time

            # Wykrywanie zmian i selektywna aktualizacja
            changed_properties = []

            # Aktualizacja właściwości tylko jeśli się zmieniły
            for key, value in state_data.items():
                if hasattr(self, key):
                    try:
                        current_value = getattr(self, key)
                        # Aktualizuj tylko jeśli wartość się zmieniła
                        if current_value != value:
                            setattr(self, key, value)
                            changed_properties.append(key)
                    except AttributeError:
                        # Ignorujemy właściwości, które nie mają settera
                        pass

            # Jeśli nie było zmian, pomijamy dalszą aktualizację
            if not changed_properties and dt is not None:
                self.updating_ui = False
                return

            # Aktualizacja właściwości alarmowych tylko jeśli są zmiany w alarmach
            if 'alarms' in changed_properties or 'has_active_alarms' in changed_properties or 'has_critical_alarms' in changed_properties:
                self.update_alarm_properties()

            # Sprawdzenie systemu klimatyzacji
            if ('climate_temp' in changed_properties or 'current_internal_temp' in changed_properties or 'auto_ac' in changed_properties or 'ac_mode' in changed_properties) and self.alarm_manager_instance:
                self.check_climate_system()

            # Bezpośrednie sprawdzenie i usunięcie alarmów baterii
            if 'battery_level' in changed_properties and self.battery_level >= 20:
                # Pobranie aktywnych alarmów
                active_alarms = self.alarm_manager_instance.get_active_alarms()
                battery_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == alarm_manager.SOURCE_BATTERY
                                 and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                if battery_alarms:
                    for alarm in battery_alarms:
                        self.alarm_manager_instance.deactivate_alarm(alarm['id'])

                    # Wymuszenie zapisania zmian
                    self.alarm_manager_instance.save_alarms()

                    # Aktualizacja właściwości alarmowych
                    self.update_alarm_properties()

                    # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                    if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                        self.root.current_screen.update_alarm_list()

            # Aktualizacja UI tylko jeśli były zmiany lub jest to aktualnie wyświetlany ekran
            if hasattr(self, 'root') and self.root and (changed_properties or dt is None):
                # Aktualizujemy tylko aktualnie wyświetlany ekran
                current_screen = self.root.current_screen
                if hasattr(current_screen, 'update_from_state'):
                    current_screen.update_from_state(0)

                # Aktualizujemy pozostałe ekrany tylko jeśli były istotne zmiany
                important_changes = any(prop in changed_properties for prop in [
                    'battery_level', 'charging', 'power_source', 'climate_temp',
                    'external_temp', 'water_level', 'fuel_level', 'engine_status',
                    'engine_throttle', 'engine_temperature', 'engine_hours',
                    'autopilot', 'interior_light_active', 'navigation_light_active',
                    'deck_light_active', 'has_active_alarms', 'has_critical_alarms'
                ])

                if important_changes:
                    for screen_name in self.root.screen_names:
                        # Pomijamy aktualny ekran, bo już został zaktualizowany
                        if screen_name == self.root.current:
                            continue

                        screen = self.root.get_screen(screen_name)
                        if hasattr(screen, 'update_from_state'):
                            screen.update_from_state(0)

                # Zwiększenie licznika aktualizacji UI
                _performance_stats['ui_updates'] += 1
        except Exception as e:
            # Ciche logowanie błędów
            pass
        finally:
            # Zresetowanie flagi
            self.updating_ui = False

    def periodic_alarm_cleanup(self, *_):
        """Cykliczne czyszczenie alarmów"""
        try:
            print("=== ROZPOCZĘCIE CYKLICZNEGO CZYSZCZENIA ALARMÓW ===")

            # Sprawdzenie, czy menedżer alarmów jest zainicjalizowany
            if not self.alarm_manager_instance:
                print("Menedżer alarmów nie jest zainicjalizowany!")
                return

            # Sprawdzenie, czy są jakieś alarmy do usunięcia
            active_alarms = self.alarm_manager_instance.get_active_alarms()
            if not active_alarms:
                print("Brak aktywnych alarmów do sprawdzenia")
                return

            print(f"Znaleziono {len(active_alarms)} aktywnych alarmów do sprawdzenia")

            # Sprawdzenie warunków systemowych, które mogą wymagać dezaktywacji alarmów
            alarms_to_remove = []

            # Sprawdzenie poziomu baterii
            if self.battery_level >= 20:
                battery_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == alarm_manager.SOURCE_BATTERY
                                 and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower())]

                if battery_alarms:
                    print(f"Znaleziono {len(battery_alarms)} alarmów baterii do usunięcia (poziom baterii: {self.battery_level}%)")
                    alarms_to_remove.extend([alarm['id'] for alarm in battery_alarms])

            # Sprawdzenie temperatury silnika
            if hasattr(self, 'engine_temperature') and self.engine_temperature <= 110:
                engine_temp_alarms = [alarm for alarm in active_alarms
                                     if alarm['source'] == alarm_manager.SOURCE_ENGINE
                                     and "temperature" in alarm['message'].lower()]

                if engine_temp_alarms:
                    print(f"Znaleziono {len(engine_temp_alarms)} alarmów temperatury silnika do usunięcia (temperatura: {self.engine_temperature}°C)")
                    alarms_to_remove.extend([alarm['id'] for alarm in engine_temp_alarms])

            # Sprawdzenie systemu klimatyzacji
            fan_power = getattr(self, 'fan_power', 0)
            ac_mode = getattr(self, 'ac_mode', 'off')

            # Sprawdzamy czy klimatyzacja jest nieaktywna (auto wyłączone i wentylator wyłączony lub tryb off)
            if hasattr(self, 'auto_ac') and not self.auto_ac and (fan_power <= 0 or ac_mode == 'off'):
                print("Klimatyzacja jest nieaktywna")
                print("Usuwanie wszystkich alarmów systemu klimatyzacji...")

                climate_alarms = [alarm for alarm in active_alarms
                                 if alarm['source'] == 'Climate'
                                 and "climate control system" in alarm['message'].lower()]

                if climate_alarms:
                    print(f"Znaleziono {len(climate_alarms)} alarmów systemu klimatyzacji do usunięcia")
                    alarms_to_remove.extend([alarm['id'] for alarm in climate_alarms])
                else:
                    print("Brak alarmów systemu klimatyzacji do usunięcia")
            elif hasattr(self, 'current_internal_temp') and hasattr(self, 'climate_temp'):
                temp_diff = abs(self.climate_temp - self.current_internal_temp)
                if temp_diff <= 3.0:
                    print(f"Różnica temperatur ({temp_diff:.1f}°C) jest w dopuszczalnym zakresie")
                    print("Usuwanie wszystkich alarmów systemu klimatyzacji...")

                    climate_alarms = [alarm for alarm in active_alarms
                                     if alarm['source'] == 'Climate'
                                     and "climate control system" in alarm['message'].lower()]

                    if climate_alarms:
                        print(f"Znaleziono {len(climate_alarms)} alarmów systemu klimatyzacji do usunięcia")
                        alarms_to_remove.extend([alarm['id'] for alarm in climate_alarms])
                    else:
                        print("Brak alarmów systemu klimatyzacji do usunięcia")

            # Sprawdzenie poziomu wody
            if self.water_level >= 10:
                water_alarms = [alarm for alarm in active_alarms
                               if alarm['source'] == alarm_manager.SOURCE_WATER
                               and "low water" in alarm['message'].lower()]

                if water_alarms:
                    print(f"Znaleziono {len(water_alarms)} alarmów poziomu wody do usunięcia (poziom wody: {self.water_level}%)")
                    alarms_to_remove.extend([alarm['id'] for alarm in water_alarms])

            # Sprawdzenie poziomu paliwa
            if self.fuel_level >= 15:
                fuel_alarms = [alarm for alarm in active_alarms
                              if alarm['source'] == alarm_manager.SOURCE_FUEL
                              and "low fuel" in alarm['message'].lower()]

                if fuel_alarms:
                    print(f"Znaleziono {len(fuel_alarms)} alarmów poziomu paliwa do usunięcia (poziom paliwa: {self.fuel_level}%)")
                    alarms_to_remove.extend([alarm['id'] for alarm in fuel_alarms])

            # Usunięcie wszystkich alarmów, które powinny być nieaktywne
            if alarms_to_remove:
                print(f"Usuwanie {len(alarms_to_remove)} alarmów...")
                for alarm_id in alarms_to_remove:
                    print(f"Usuwanie alarmu: {alarm_id}")
                    self.alarm_manager_instance.deactivate_alarm(alarm_id)

                # Wymuszenie zapisania zmian
                self.alarm_manager_instance.save_alarms()

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

                # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

                print(f"Usunięto {len(alarms_to_remove)} alarmów")
            else:
                print("Brak alarmów do usunięcia")

            print("=== ZAKOŃCZENIE CYKLICZNEGO CZYSZCZENIA ALARMÓW ===")
        except Exception as e:
            print(f"Błąd podczas cyklicznego czyszczenia alarmów: {e}")
            import traceback
            traceback.print_exc()

    def on_stop(self):
        # Zatrzymanie cyklicznego wczytywania stanu
        if hasattr(self, 'state_update_event'):
            self.state_update_event.cancel()

        # Zatrzymanie cyklicznego czyszczenia alarmów
        if hasattr(self, 'alarm_cleanup_event'):
            self.alarm_cleanup_event.cancel()

        # Zatrzymanie symulacji klimatyzacji
        if hasattr(self, 'climate_simulation_event'):
            self.climate_simulation_event.cancel()

        # Zatrzymanie monitora alarmów
        if hasattr(self, 'alarm_monitor'):
            print("=== ZATRZYMYWANIE MONITORA ALARMÓW ===")
            alarm_monitor.stop_monitoring()
            print("=== MONITOR ALARMÓW ZATRZYMANY ===")

        # Cleanup buzzer controller
        try:
            cleanup_buzzer()
            print("Buzzer controller cleaned up")
        except Exception as e:
            print(f"Error cleaning up buzzer controller: {e}")

        # Nie zapisujemy stanu przy zamknięciu aplikacji - panel kontrolny jest odpowiedzialny za stan

    # Metody obsługi alarmów
    def add_alarm(self, alarm_type, message, source):
        """Dodaje nowy alarm do systemu"""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.add_alarm(alarm_type, message, source)
        return None

    def acknowledge_alarm(self, alarm_id):
        """Potwierdza alarm o podanym ID"""
        if self.alarm_manager_instance:
            result = self.alarm_manager_instance.acknowledge_alarm(alarm_id)
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

            return result
        return False

    def confirm_critical_alarms(self):
        """
        Confirm/acknowledge all critical alarms and deactivate buzzer.
        This method is called by the "CONFIRM ALARM" button.
        """
        if self.alarm_manager_instance:
            try:
                # Acknowledge all critical and emergency alarms
                acknowledged_count = self.alarm_manager_instance.acknowledge_all_critical_alarms("Bridge Operator")

                # Update alarm properties to refresh UI
                self.update_alarm_properties()

                # Refresh alarm screen if currently displayed
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

                print(f"Confirmed {acknowledged_count} critical alarms and deactivated buzzer")
                return acknowledged_count

            except Exception as e:
                print(f"Error confirming critical alarms: {e}")
                return 0
        return 0

    def get_buzzer_status(self):
        """Get current buzzer status for UI display."""
        if self.buzzer_controller:
            return self.buzzer_controller.is_buzzer_active()
        return False

    def force_buzzer_stop(self):
        """Emergency buzzer stop (for testing or emergency override)."""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.force_buzzer_deactivation("Emergency Override")
        return False

    def acknowledge_all_alarms(self):
        """Potwierdza wszystkie aktywne alarmy"""
        if self.alarm_manager_instance:
            self.alarm_manager_instance.acknowledge_all_alarms()
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

    def deactivate_alarm(self, alarm_id):
        """Dezaktywuje alarm o podanym ID"""
        if self.alarm_manager_instance:
            result = self.alarm_manager_instance.deactivate_alarm(alarm_id)
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

            return result
        return False

    def deactivate_alarm_if_inactive(self, alarm_id, is_active):
        """Dezaktywuje alarm, jeśli jest nieaktywny"""
        if self.alarm_manager_instance:
            result = self.alarm_manager_instance.deactivate_alarm_if_inactive(alarm_id, is_active)
            if result:
                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

                # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

            return result
        return False

    def deactivate_all_alarms(self):
        """Dezaktywuje wszystkie alarmy"""
        if self.alarm_manager_instance:
            self.alarm_manager_instance.deactivate_all_alarms()
            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

            # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
            if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                self.root.current_screen.update_alarm_list()

    def get_active_alarms(self, alarm_types=None):
        """Zwraca listę aktywnych alarmów, opcjonalnie filtrowaną po typach"""
        if self.alarm_manager_instance:
            alarms = self.alarm_manager_instance.get_active_alarms(alarm_types)
            print(f"MainApp.get_active_alarms: znaleziono {len(alarms)} alarmów")
            for alarm in alarms:
                print(f"  - {alarm.get('message', 'Brak wiadomości')} ({alarm.get('type', 'Brak typu')})")
            return alarms
        print("MainApp.get_active_alarms: alarm_manager_instance jest None!")
        return []

    def get_alarm_history(self, limit=50, alarm_types=None):
        """Zwraca historię alarmów, opcjonalnie filtrowaną po typach"""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.get_alarm_history(limit, alarm_types)
        return []

    def update_alarm(self, alarm_id, message):
        """Aktualizuje treść alarmu"""
        if self.alarm_manager_instance:
            return self.alarm_manager_instance.update_alarm(alarm_id, message)
        return False

    def update_alarm_properties(self):
        """Aktualizuje właściwości alarmowe na podstawie stanu menedżera alarmów"""
        if self.alarm_manager_instance:
            self.has_active_alarms = self.alarm_manager_instance.has_active_alarms()
            self.has_critical_alarms = self.alarm_manager_instance.has_critical_alarms()
            self.alarm_status = self.alarm_manager_instance.get_system_status()

    def check_battery_level(self):
        """Sprawdza poziom baterii i dodaje/aktualizuje alarm, gdy poziom jest poniżej 20%"""
        print(f"MainApp.check_battery_level: Sprawdzanie poziomu baterii: {self.battery_level}%")
        if self.alarm_manager_instance:
            # Pobranie aktywnych alarmów baterii
            existing_alarms = self.get_active_alarms()
            battery_alarm_id = None
            battery_alarms = []

            # Sprawdzenie, czy istnieje alarm o niskim poziomie baterii
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_BATTERY and ("low battery" in alarm['message'].lower() or "niski poziom baterii" in alarm['message'].lower()):
                    battery_alarm_id = alarm['id']
                    battery_alarms.append(alarm)
                    print(f"MainApp.check_battery_level: Znaleziono istniejący alarm baterii: {alarm['id']}, wiadomość: {alarm['message']}, aktywny: {alarm.get('active', True)}")

            print(f"MainApp.check_battery_level: Znaleziono {len(battery_alarms)} alarmów baterii")

            # Sprawdzenie, czy poziom baterii jest poniżej 20%
            if self.battery_level < 20:
                # Przygotowanie treści alarmu
                alarm_message = f"Critically low battery level! Only {int(self.battery_level)}% power remaining."

                if not battery_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    battery_alarm_id = self.alarm_manager_instance.add_battery_alarm(
                        alarm_message,
                        alarm_manager.ALARM_CRITICAL
                    )
                    print(f"MainApp.check_battery_level: Dodano alarm krytyczny: niski poziom baterii ({int(self.battery_level)}%)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_battery_alarm(
                        battery_alarm_id,
                        alarm_message
                    )
                    print(f"MainApp.check_battery_level: Zaktualizowano alarm krytyczny: niski poziom baterii ({int(self.battery_level)}%)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif self.battery_level >= 20:
                # Jeśli poziom baterii wzrósł powyżej 20%, dezaktywuj wszystkie alarmy baterii
                print(f"MainApp.check_battery_level: Poziom baterii ({int(self.battery_level)}%) jest powyżej progu krytycznego (20%)")

                if battery_alarms:
                    print(f"MainApp.check_battery_level: Dezaktywacja {len(battery_alarms)} alarmów baterii")
                    for alarm in battery_alarms:
                        print(f"MainApp.check_battery_level: Dezaktywacja alarmu baterii {alarm['id']} - poziom baterii wzrósł do {int(self.battery_level)}%")
                        result = self.alarm_manager_instance.deactivate_alarm(alarm['id'])
                        print(f"MainApp.check_battery_level: Wynik dezaktywacji alarmu {alarm['id']}: {result}")
                else:
                    print("MainApp.check_battery_level: Brak alarmów baterii do dezaktywacji")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

                # Odświeżenie ekranu alarmów, jeśli jest aktualnie wyświetlany
                if hasattr(self, 'root') and self.root and self.root.current == 'alarm':
                    self.root.current_screen.update_alarm_list()

    def check_fuel_level(self):
        """Sprawdza poziom paliwa i dodaje/dezaktywuje alarmy"""
        if self.alarm_manager_instance:
            # Pobranie aktywnych alarmów paliwa
            existing_alarms = self.get_active_alarms()
            fuel_level_alarm_id = None

            # Sprawdzenie, czy istnieje alarm o niskim poziomie paliwa
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_FUEL and "low fuel" in alarm['message'].lower():
                    fuel_level_alarm_id = alarm['id']
                    break

            # Krytyczny poziom paliwa - poniżej 15%
            if self.fuel_level < 15:
                # Przygotowanie treści alarmu
                alarm_message = f"Low fuel level! Only {int(self.fuel_level)}% remaining."

                if not fuel_level_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    fuel_level_alarm_id = self.alarm_manager_instance.add_fuel_alarm(
                        alarm_message,
                        alarm_manager.ALARM_WARNING
                    )
                    print(f"Dodano alarm ostrzegawczy: niski poziom paliwa ({int(self.fuel_level)}%)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_alarm(
                        fuel_level_alarm_id,
                        alarm_message
                    )
                    print(f"Zaktualizowano alarm ostrzegawczy: niski poziom paliwa ({int(self.fuel_level)}%)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif fuel_level_alarm_id and self.fuel_level >= 15:
                # Jeśli poziom paliwa wzrósł powyżej krytycznego poziomu, dezaktywuj alarm
                self.alarm_manager_instance.deactivate_alarm(fuel_level_alarm_id)
                print(f"Dezaktywowano alarm o niskim poziomie paliwa - poziom wzrósł do {int(self.fuel_level)}%")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

    def check_water_level(self):
        """Sprawdza poziom wody i dodaje/dezaktywuje alarmy"""
        if self.alarm_manager_instance:
            # Pobranie aktywnych alarmów wody
            existing_alarms = self.get_active_alarms()
            water_level_alarm_id = None

            # Sprawdzenie, czy istnieje alarm o niskim poziomie wody
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_WATER and "low water" in alarm['message'].lower():
                    water_level_alarm_id = alarm['id']
                    break

            # Krytyczny poziom wody - poniżej 10%
            if self.water_level < 10:
                # Przygotowanie treści alarmu
                alarm_message = f"Critically low water level! Only {int(self.water_level)}% remaining."

                if not water_level_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    water_level_alarm_id = self.alarm_manager_instance.add_water_alarm(
                        alarm_message,
                        alarm_manager.ALARM_WARNING
                    )
                    print(f"Dodano alarm ostrzegawczy: niski poziom wody ({int(self.water_level)}%)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_alarm(
                        water_level_alarm_id,
                        alarm_message
                    )
                    print(f"Zaktualizowano alarm ostrzegawczy: niski poziom wody ({int(self.water_level)}%)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif water_level_alarm_id and self.water_level >= 10:
                # Jeśli poziom wody wzrósł powyżej krytycznego poziomu, dezaktywuj alarm
                self.alarm_manager_instance.deactivate_alarm(water_level_alarm_id)
                print(f"Dezaktywowano alarm o niskim poziomie wody - poziom wzrósł do {int(self.water_level)}%")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

    def check_engine_temperature(self):
        """Sprawdza temperaturę silnika i dodaje/dezaktywuje alarmy"""
        if self.alarm_manager_instance and hasattr(self, 'engine_temperature'):
            # Pobranie aktywnych alarmów silnika
            existing_alarms = self.get_active_alarms()
            engine_temp_alarm_id = None

            # Sprawdzenie, czy istnieje alarm o wysokiej temperaturze silnika
            for alarm in existing_alarms:
                if alarm['source'] == alarm_manager.SOURCE_ENGINE and "temperature" in alarm['message'].lower():
                    engine_temp_alarm_id = alarm['id']
                    break

            # Krytyczna temperatura silnika - powyżej 110°C
            if hasattr(self, 'engine_temperature') and self.engine_temperature > 110:
                # Przygotowanie treści alarmu
                alarm_message = f"Critical engine temperature! Current temperature: {self.engine_temperature:.1f}°C"

                if not engine_temp_alarm_id:
                    # Jeśli nie ma jeszcze alarmu, dodaj go
                    engine_temp_alarm_id = self.alarm_manager_instance.add_engine_alarm(
                        alarm_message,
                        alarm_manager.ALARM_CRITICAL
                    )
                    print(f"Dodano alarm krytyczny: wysoka temperatura silnika ({self.engine_temperature:.1f}°C)")
                else:
                    # Jeśli alarm już istnieje, zaktualizuj jego treść
                    self.alarm_manager_instance.update_alarm(
                        engine_temp_alarm_id,
                        alarm_message
                    )
                    print(f"Zaktualizowano alarm krytyczny: wysoka temperatura silnika ({self.engine_temperature:.1f}°C)")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()
            elif engine_temp_alarm_id and hasattr(self, 'engine_temperature') and self.engine_temperature <= 110:
                # Jeśli temperatura silnika spadła poniżej krytycznego poziomu, dezaktywuj alarm
                self.alarm_manager_instance.deactivate_alarm(engine_temp_alarm_id)
                print(f"Dezaktywowano alarm o wysokiej temperaturze silnika - temperatura spadła do {self.engine_temperature:.1f}°C")

                # Aktualizacja właściwości alarmowych
                self.update_alarm_properties()

    def simulate_climate_system(self, dt):
        """
        Symulacja systemu klimatyzacji - aktualizuje current_internal_temp na podstawie
        external_temp, climate_temp, fan_power, ac_mode i auto_ac
        """
        try:
            # Pobranie aktualnych wartości
            external_temp = getattr(self, 'external_temp', 14)
            target_temp = getattr(self, 'climate_temp', 22)
            current_temp = getattr(self, 'current_internal_temp', 22)
            auto_ac = getattr(self, 'auto_ac', True)
            fan_power = getattr(self, 'fan_power', 50)
            ac_mode = getattr(self, 'ac_mode', 'off')

            # Zapisanie początkowych wartości do wykrycia zmian
            original_current_temp = current_temp
            original_fan_power = fan_power
            original_ac_mode = ac_mode

            # Obliczenie wpływu temperatury zewnętrznej na temperaturę wewnętrzną
            # Im większa różnica, tym szybciej temperatura wewnętrzna dąży do zewnętrznej
            external_influence = (external_temp - current_temp) * 0.02 * dt

            if auto_ac:
                # Tryb automatyczny - system sam steruje klimatyzacją
                temp_diff = target_temp - current_temp

                # Określenie trybu pracy klimatyzacji
                if temp_diff > 1.0:  # Potrzebne ogrzewanie
                    ac_mode = "heating"
                    # Moc wentylatora zależy od różnicy temperatur
                    fan_power = min(100, 50 + abs(temp_diff) * 10)
                elif temp_diff < -1.0:  # Potrzebne chłodzenie
                    ac_mode = "cooling"
                    # Moc wentylatora zależy od różnicy temperatur
                    fan_power = min(100, 50 + abs(temp_diff) * 10)
                else:  # Temperatura w normie
                    ac_mode = "off"
                    fan_power = 0

                # Obliczenie wpływu klimatyzacji na temperaturę wewnętrzną
                ac_influence = 0
                if ac_mode == "heating":
                    # Ogrzewanie - im większa moc wentylatora, tym szybciej rośnie temperatura
                    ac_influence = (fan_power / 100.0) * 0.5 * dt
                elif ac_mode == "cooling":
                    # Chłodzenie - im większa moc wentylatora, tym szybciej spada temperatura
                    ac_influence = -(fan_power / 100.0) * 0.5 * dt

                # Aktualizacja temperatury wewnętrznej
                new_temp = current_temp + external_influence + ac_influence
            else:
                # Jeśli auto_ac jest wyłączone, użytkownik steruje ręcznie
                # Tryb pracy zależy od różnicy temperatur
                temp_diff = target_temp - current_temp

                # Określenie trybu pracy klimatyzacji na podstawie różnicy temperatur
                if temp_diff > 1.0 and fan_power > 0:
                    ac_mode = "heating"
                    # Obliczenie wpływu ogrzewania na temperaturę wewnętrzną
                    # Im większa moc wentylatora, tym szybciej rośnie temperatura
                    ac_influence = (fan_power / 100.0) * 0.3 * dt
                elif temp_diff < -1.0 and fan_power > 0:
                    ac_mode = "cooling"
                    # Obliczenie wpływu chłodzenia na temperaturę wewnętrzną
                    # Im większa moc wentylatora, tym szybciej spada temperatura
                    ac_influence = -(fan_power / 100.0) * 0.3 * dt
                else:
                    ac_mode = "off"
                    # Jeśli wentylator jest włączony, ale nie ma potrzeby ogrzewania ani chłodzenia,
                    # to temperatura wewnętrzna dąży do temperatury zewnętrznej
                    if fan_power > 0:
                        ac_influence = (external_temp - current_temp) * (fan_power / 100.0) * 0.2 * dt
                    else:
                        ac_influence = 0

                # Aktualizacja temperatury wewnętrznej
                new_temp = current_temp + external_influence + ac_influence

            # Ograniczenie temperatury wewnętrznej do sensownego zakresu
            new_temp = max(10, min(35, new_temp))

            # Sprawdzenie, czy wartości się zmieniły
            temp_changed = abs(original_current_temp - new_temp) > 0.1
            fan_changed = original_fan_power != fan_power
            mode_changed = original_ac_mode != ac_mode

            # Aktualizacja tylko jeśli wartości się zmieniły
            if temp_changed or fan_changed or mode_changed:
                self.current_internal_temp = new_temp
                self.fan_power = fan_power
                self.ac_mode = ac_mode

                # Zapisanie zmian do pliku stanu
                self.save_state_to_file()

        except Exception as e:
            # Ciche logowanie błędów symulacji
            pass

    def check_climate_system(self):
        """Sprawdza system klimatyzacji i dodaje/dezaktywuje alarmy"""
        if not self.alarm_manager_instance:
            print("Menedżer alarmów nie jest zainicjalizowany!")
            return

        # Sprawdzenie, czy mamy wszystkie potrzebne dane
        if not hasattr(self, 'climate_temp') or not hasattr(self, 'current_internal_temp') or not hasattr(self, 'auto_ac'):
            print("Brak wymaganych danych klimatyzacji!")
            return

        print(f"Sprawdzanie systemu klimatyzacji: zadana={self.climate_temp:.1f}°C, aktualna={self.current_internal_temp:.1f}°C, auto_ac={self.auto_ac}")

        # Sprawdzamy zarówno dla trybu auto jak i manualnego, ale tylko gdy klimatyzacja jest aktywna
        fan_power = getattr(self, 'fan_power', 0)
        ac_mode = getattr(self, 'ac_mode', 'off')

        if not self.auto_ac and (fan_power <= 0 or ac_mode == 'off'):
            print("Klimatyzacja jest nieaktywna, pomijanie sprawdzania")
            return

        # Pobranie aktywnych alarmów klimatyzacji
        existing_alarms = self.get_active_alarms()
        climate_alarms = {
            'warning': None,
            'caution': None
        }

        # Sprawdzenie, czy istnieją alarmy klimatyzacji
        for alarm in existing_alarms:
            if alarm['source'] == 'Climate' and "climate control system" in alarm['message'].lower():
                if alarm['type'] == alarm_manager.ALARM_WARNING:
                    climate_alarms['warning'] = alarm['id']
                elif alarm['type'] == alarm_manager.ALARM_CAUTION:
                    climate_alarms['caution'] = alarm['id']
                print(f"Znaleziono istniejący alarm klimatyzacji: {alarm['id']}, typ: {alarm['type']}, wiadomość: {alarm['message']}")

        # Obliczenie różnicy temperatur
        temp_diff = abs(self.climate_temp - self.current_internal_temp)
        ac_mode = getattr(self, 'ac_mode', 'off')

        # Sprawdzenie, czy system klimatyzacji działa prawidłowo
        if temp_diff > 5.0 and (
            (ac_mode == "heating" and self.climate_temp > self.current_internal_temp) or
            (ac_mode == "cooling" and self.climate_temp < self.current_internal_temp)
        ):
            # Przygotowanie treści alarmu
            alarm_message = f"WARNING: Climate control system may be malfunctioning. Target: {self.climate_temp:.1f}°C, Actual: {self.current_internal_temp:.1f}°C"

            if not climate_alarms['warning']:
                # Jeśli nie ma jeszcze alarmu ostrzegawczego, dodaj go
                climate_alarms['warning'] = self.alarm_manager_instance.add_alarm(
                    alarm_manager.ALARM_WARNING,
                    alarm_message,
                    'Climate',
                    category=alarm_manager.CATEGORY_B  # Kategoria B dla ostrzeżeń
                )
                print(f"Dodano alarm ostrzegawczy: problem z systemem klimatyzacji (różnica: {temp_diff:.1f}°C)")
            else:
                # Jeśli alarm już istnieje, zaktualizuj jego treść
                self.alarm_manager_instance.update_alarm(
                    climate_alarms['warning'],
                    alarm_message
                )
                print(f"Zaktualizowano alarm ostrzegawczy: problem z systemem klimatyzacji (różnica: {temp_diff:.1f}°C)")

            # Dezaktywacja alarmów o niższym priorytecie
            if climate_alarms['caution']:
                self.alarm_manager_instance.deactivate_alarm(climate_alarms['caution'])
                print(f"Dezaktywowano alarm ostrożności klimatyzacji - zastąpiony alarmem ostrzegawczym")

            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()
        elif temp_diff > 3.0 and temp_diff <= 5.0 and (
            (ac_mode == "heating" and self.climate_temp > self.current_internal_temp) or
            (ac_mode == "cooling" and self.climate_temp < self.current_internal_temp)
        ):
            # Przygotowanie treści alarmu
            alarm_message = f"CAUTION: Climate control system response slow. Target: {self.climate_temp:.1f}°C, Actual: {self.current_internal_temp:.1f}°C"

            # Dezaktywacja alarmów o wyższym priorytecie, jeśli stan się poprawił
            if climate_alarms['warning']:
                self.alarm_manager_instance.deactivate_alarm(climate_alarms['warning'])
                print(f"Dezaktywowano alarm ostrzegawczy klimatyzacji - stan poprawił się do poziomu ostrożności")

            if not climate_alarms['caution']:
                # Jeśli nie ma jeszcze alarmu ostrożności, dodaj go
                climate_alarms['caution'] = self.alarm_manager_instance.add_alarm(
                    alarm_manager.ALARM_CAUTION,
                    alarm_message,
                    'Climate',
                    category=alarm_manager.CATEGORY_C  # Kategoria C dla ostrożności
                )
                print(f"Dodano alarm ostrożności: powolna reakcja systemu klimatyzacji (różnica: {temp_diff:.1f}°C)")
            else:
                # Jeśli alarm już istnieje, zaktualizuj jego treść
                self.alarm_manager_instance.update_alarm(
                    climate_alarms['caution'],
                    alarm_message
                )
                print(f"Zaktualizowano alarm ostrożności: powolna reakcja systemu klimatyzacji (różnica: {temp_diff:.1f}°C)")

            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()
        else:
            # Jeśli system klimatyzacji działa prawidłowo, dezaktywuj wszystkie alarmy
            print(f"System klimatyzacji działa prawidłowo. Zadana: {self.climate_temp:.1f}°C, Aktualna: {self.current_internal_temp:.1f}°C, Różnica: {temp_diff:.1f}°C")

            # Dezaktywacja wszystkich alarmów klimatyzacji
            for level, alarm_id in climate_alarms.items():
                if alarm_id:
                    self.alarm_manager_instance.deactivate_alarm(alarm_id)
                    print(f"Dezaktywowano alarm {level} klimatyzacji - system działa prawidłowo")

            # Aktualizacja właściwości alarmowych
            self.update_alarm_properties()

    def go_to_alarm_screen(self):
        """Przechodzi do ekranu alarmów"""
        if hasattr(self, 'root'):
            self.root.current = 'alarm'

def launch_control_panel():
    """Uruchamia panel kontrolny w osobnym procesie"""
    import subprocess
    import sys
    import threading

    def run_control_panel():
        try:
            # Uruchomienie panelu kontrolnego w osobnym procesie
            # Dzięki implementacji wzorca Singleton w SystemState,
            # panel kontrolny będzie korzystał z tej samej instancji stanu
            subprocess.Popen([sys.executable, "control_panel.py"],
                            cwd=os.path.dirname(os.path.abspath(__file__)))
            print("Panel kontrolny uruchomiony")
        except Exception as e:
            print(f"Błąd uruchamiania panelu kontrolnego: {e}")

    # Uruchomienie w osobnym wątku, aby nie blokować głównej aplikacji
    thread = threading.Thread(target=run_control_panel)
    thread.daemon = True  # Wątek zostanie zakończony, gdy główna aplikacja się zakończy
    thread.start()

if __name__ == "__main__":
    # Uruchomienie panelu kontrolnego
    launch_control_panel()

    # Uruchomienie głównej aplikacji
    MainApp().run()
