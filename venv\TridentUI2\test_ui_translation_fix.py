#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test for UI Translation Fix
Tests if the UI translation system works correctly after fixes
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_translation_system():
    """Test the translation system"""
    print("🧪 Testing Translation System")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language, get_global_language
        from translatable_screen import tr
        
        # Test basic translation functionality
        print("\n📝 Testing basic translations...")
        
        test_keys = [
            "HOME", "LIGHTNING", "ENGINE", "BATTERY",
            "NAVIGATION LIGHTNING", "INTERIOR LIGHTNING", 
            "ENGINE CONTROL", "ENGINE STATUS"
        ]
        
        languages = ["English", "Polski", "Deutsch", "Русский"]
        
        for language in languages:
            print(f"\n🌍 Testing {language}:")
            set_global_language(language)
            
            for key in test_keys[:4]:  # Test first 4 keys
                translation = tr(key)
                print(f"  {key}: {translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_translation_method():
    """Test app translation method"""
    print("\n🧪 Testing App Translation Method")
    print("=" * 50)
    
    try:
        import main
        
        # Create app instance
        app = main.MainApp()
        
        # Test get_translation method
        print("\n📝 Testing app.get_translation()...")
        
        test_keys = ["HOME", "ENGINE", "LIGHTNING", "BATTERY"]
        
        for key in test_keys:
            translation = app.get_translation(key)
            print(f"  app.get_translation('{key}'): {translation}")
        
        # Test with different languages
        print("\n🌍 Testing with different languages...")
        
        for language in ["Polski", "Deutsch"]:
            print(f"\n  Language: {language}")
            for key in test_keys[:2]:  # Test first 2 keys
                translation = app.get_translation(key, language)
                print(f"    {key}: {translation}")
        
        return True
        
    except Exception as e:
        print(f"❌ App translation method test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_screen_translation_mappings():
    """Test screen translation mappings"""
    print("\n🧪 Testing Screen Translation Mappings")
    print("=" * 50)
    
    try:
        import main
        
        # Create app instance
        app = main.MainApp()
        
        # Test update_screen_translations method
        print("\n📝 Testing screen translation mappings...")
        
        # Create a mock screen with IDs
        class MockScreen:
            def __init__(self):
                self.ids = {}
                # Add mock widgets
                for widget_id in ['home_title_label', 'lightning_label', 'engine_label']:
                    mock_widget = MockWidget()
                    self.ids[widget_id] = mock_widget
        
        class MockWidget:
            def __init__(self):
                self.text = "Original Text"
        
        # Test home screen mappings
        mock_screen = MockScreen()
        
        print("  Testing home screen mappings...")
        app.update_screen_translations(mock_screen, 'home')
        
        # Check if widgets were updated
        for widget_id, widget in mock_screen.ids.items():
            print(f"    {widget_id}: {widget.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Screen translation mappings test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_kv_file_changes():
    """Test if .kv files have been updated correctly"""
    print("\n🧪 Testing KV File Changes")
    print("=" * 50)
    
    try:
        # Check home.kv
        print("\n📄 Checking home.kv...")
        with open('ui/home.kv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for app.get_translation usage
        translation_calls = content.count('app.get_translation')
        print(f"  Found {translation_calls} app.get_translation calls")
        
        if translation_calls > 0:
            print("  ✅ home.kv has been updated with translation calls")
        else:
            print("  ❌ home.kv does not have translation calls")
        
        # Check lightning.kv
        print("\n📄 Checking lightning.kv...")
        with open('ui/lightning.kv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        translation_calls = content.count('app.get_translation')
        print(f"  Found {translation_calls} app.get_translation calls")
        
        if translation_calls > 0:
            print("  ✅ lightning.kv has been updated with translation calls")
        else:
            print("  ❌ lightning.kv does not have translation calls")
        
        # Check engine.kv
        print("\n📄 Checking engine.kv...")
        with open('ui/engine.kv', 'r', encoding='utf-8') as f:
            content = f.read()
        
        translation_calls = content.count('app.get_translation')
        print(f"  Found {translation_calls} app.get_translation calls")
        
        if translation_calls > 0:
            print("  ✅ engine.kv has been updated with translation calls")
        else:
            print("  ❌ engine.kv does not have translation calls")
        
        return translation_calls > 0
        
    except Exception as e:
        print(f"❌ KV file changes test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_global_language_system():
    """Test the complete global language system"""
    print("\n🧪 Testing Complete Global Language System")
    print("=" * 50)
    
    try:
        from language_manager import set_global_language, get_global_language
        import main
        
        # Create app instance
        app = main.MainApp()
        
        print("\n🔄 Testing global language changes...")
        
        # Test language changes
        for language in ["Polski", "Deutsch", "English"]:
            print(f"\n🌍 Setting language to {language}...")
            success = set_global_language(language)
            
            if success:
                current = get_global_language()
                print(f"  ✅ Language set successfully: {current}")
                
                # Test app translation
                home_translation = app.get_translation("HOME")
                engine_translation = app.get_translation("ENGINE")
                print(f"  HOME: {home_translation}")
                print(f"  ENGINE: {engine_translation}")
            else:
                print(f"  ❌ Failed to set language to {language}")
        
        return True
        
    except Exception as e:
        print(f"❌ Global language system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Starting UI Translation Fix Tests")
    print("=" * 60)
    
    tests = [
        test_translation_system,
        test_app_translation_method,
        test_screen_translation_mappings,
        test_kv_file_changes,
        test_global_language_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ Test passed")
            else:
                print("❌ Test failed")
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        
        print("-" * 60)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! UI translation system should work correctly!")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
